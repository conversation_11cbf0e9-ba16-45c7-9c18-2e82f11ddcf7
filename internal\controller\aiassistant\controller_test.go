package aiassistant

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/aiassistant"
	aiassistantService "github.com/dsoplabs/dinbora-backend/internal/service/aiassistant"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// Mock AI Assistant Service
type MockAIAssistantService struct {
	mock.Mock
}

// Ensure MockAIAssistantService implements the service interface
var _ aiassistantService.Service = (*MockAIAssistantService)(nil)

func (m *MockAIAssistantService) FinancialHealthOverview(ctx context.Context, userID string) (*aiassistant.FinancialHealthOverviewDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.FinancialHealthOverviewDTO), args.Error(1)
}

func (m *MockAIAssistantService) DreamProgressAnalysis(ctx context.Context, userID string) (*aiassistant.DreamProgressAnalysisDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.DreamProgressAnalysisDTO), args.Error(1)
}

func (m *MockAIAssistantService) LearningPathData(ctx context.Context, userID string) (*aiassistant.LearningPathDataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.LearningPathDataDTO), args.Error(1)
}

func (m *MockAIAssistantService) SpendingAnalysisData(ctx context.Context, userID string) (*aiassistant.SpendingAnalysisDataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.SpendingAnalysisDataDTO), args.Error(1)
}

func (m *MockAIAssistantService) InvestmentProfileData(ctx context.Context, userID string) (*aiassistant.InvestmentProfileDataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.InvestmentProfileDataDTO), args.Error(1)
}

func (m *MockAIAssistantService) FinancialIndependenceData(ctx context.Context, userID string) (*aiassistant.FinancialIndependenceDataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.FinancialIndependenceDataDTO), args.Error(1)
}

func (m *MockAIAssistantService) GamificationEngagementData(ctx context.Context, userID string) (*aiassistant.GamificationEngagementDataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.GamificationEngagementDataDTO), args.Error(1)
}

func (m *MockAIAssistantService) FamilyFinancialDNAData(ctx context.Context, userID string) (*aiassistant.FamilyFinancialDNADataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.FamilyFinancialDNADataDTO), args.Error(1)
}

func (m *MockAIAssistantService) TransactionBehaviorData(ctx context.Context, userID string) (*aiassistant.TransactionBehaviorDataDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.TransactionBehaviorDataDTO), args.Error(1)
}

func (m *MockAIAssistantService) ComprehensiveFinancialContext(ctx context.Context, userID string) (*aiassistant.ComprehensiveFinancialContextDTO, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*aiassistant.ComprehensiveFinancialContextDTO), args.Error(1)
}

// Test suite
type AIAssistantControllerTestSuite struct {
	suite.Suite
	controller   Controller
	mockService  *MockAIAssistantService
	echo         *echo.Echo
	testUserID   string
	testUserRole string
}

func (suite *AIAssistantControllerTestSuite) SetupTest() {
	// Set up test environment variables for JWT
	os.Setenv("API_ACCESS_JWT_KEY", "test-jwt-key")
	os.Setenv("API_REFRESH_JWT_KEY", "test-refresh-jwt-key")

	suite.mockService = new(MockAIAssistantService)
	suite.controller = New(suite.mockService)
	suite.echo = echo.New()
	suite.testUserID = "test-user-123"
	suite.testUserRole = "user"
}

func (suite *AIAssistantControllerTestSuite) TearDownTest() {
	// Clean up environment variables
	os.Unsetenv("API_ACCESS_JWT_KEY")
	os.Unsetenv("API_REFRESH_JWT_KEY")
}

func TestAIAssistantControllerTestSuite(t *testing.T) {
	suite.Run(t, new(AIAssistantControllerTestSuite))
}

func (suite *AIAssistantControllerTestSuite) createTestJWT() (string, error) {
	// Create a test user for JWT generation
	testUser := &model.User{
		ID:    suite.testUserID,
		Roles: []string{suite.testUserRole},
	}

	// Generate JWT token
	tokens, err := token.Create(testUser)
	if err != nil {
		return "", err
	}

	return tokens.Access, nil
}

func (suite *AIAssistantControllerTestSuite) TestFinancialHealthOverview_Success() {
	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Mock service response
	expectedResponse := &aiassistant.FinancialHealthOverviewDTO{
		UserID: suite.testUserID,
		MonthlyFinancials: aiassistant.MonthlyFinancialsDTO{
			Income:           10000,
			Expenses:         6000,
			Savings:          4000,
			SavingsRate:      40.0,
			FixedExpenses:    3500,
			VariableExpenses: 2000,
			DebtPayments:     500,
		},
		StressMetrics: aiassistant.StressMetricsDTO{
			CommitmentRatio:     0.6,
			EmergencyFundMonths: 3.5,
			DebtToIncomeRatio:   0.05,
			ExpenseGrowthRate:   2.5,
		},
		NetWorthData: aiassistant.NetWorthDataDTO{
			Current:       150000,
			PreviousMonth: 145000,
			ChangePercent: 3.45,
			StrategicFund: 30000,
			Investments:   70000,
			Assets:        50000,
			Debts:         0,
		},
		HistoricalTrends: []aiassistant.HistoricalTrendDTO{
			{
				Month:    "2024-12",
				NetWorth: 150000,
				Income:   10000,
				Expenses: 6000,
			},
			{
				Month:    "2024-11",
				NetWorth: 145000,
				Income:   9500,
				Expenses: 5800,
			},
		},
		DataTimestamp: time.Now().Format(time.RFC3339),
	}

	suite.mockService.On("FinancialHealthOverview", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/financial-health-overview", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Call handler
	handler := suite.controller.FinancialHealthOverview()
	err = handler(c)

	// Verify results
	suite.Require().NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Parse response
	var response aiassistant.FinancialHealthOverviewDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)

	// Verify response content
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal(int64(10000), response.MonthlyFinancials.Income)
	suite.Equal(int64(6000), response.MonthlyFinancials.Expenses)
	suite.Equal(int64(4000), response.MonthlyFinancials.Savings)
	suite.Equal(40.0, response.MonthlyFinancials.SavingsRate)

	suite.Equal(0.6, response.StressMetrics.CommitmentRatio)
	suite.Equal(3.5, response.StressMetrics.EmergencyFundMonths)
	suite.Equal(0.05, response.StressMetrics.DebtToIncomeRatio)
	suite.Equal(2.5, response.StressMetrics.ExpenseGrowthRate)

	suite.Equal(int64(150000), response.NetWorthData.Current)
	suite.Equal(int64(145000), response.NetWorthData.PreviousMonth)
	suite.InDelta(3.45, response.NetWorthData.ChangePercent, 0.01)

	suite.Len(response.HistoricalTrends, 2)
	suite.Equal("2024-12", response.HistoricalTrends[0].Month)
	suite.Equal(int64(150000), response.HistoricalTrends[0].NetWorth)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestFinancialHealthOverview_Unauthorized() {
	// Create request without authorization header
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/financial-health-overview", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Call handler
	handler := suite.controller.FinancialHealthOverview()
	err := handler(c)

	// Should return unauthorized error
	suite.Error(err)
	suite.Contains(err.Error(), "failed to extract user claims")
}

func (suite *AIAssistantControllerTestSuite) TestFinancialHealthOverview_InvalidToken() {
	// Create request with invalid token
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/financial-health-overview", nil)
	req.Header.Set("Authorization", "Bearer invalid-token")
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Call handler
	handler := suite.controller.FinancialHealthOverview()
	err := handler(c)

	// Should return unauthorized error
	suite.Error(err)
	suite.Contains(err.Error(), "failed to extract user claims")
}

func (suite *AIAssistantControllerTestSuite) TestFinancialHealthOverview_ServiceError() {
	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Mock service to return error
	suite.mockService.On("FinancialHealthOverview", mock.Anything, suite.testUserID).Return(nil, assert.AnError)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/financial-health-overview", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Call handler
	handler := suite.controller.FinancialHealthOverview()
	err = handler(c)

	// Should return the service error
	suite.Error(err)
	suite.Equal(assert.AnError, err)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestDreamProgressAnalysis_Success() {
	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Mock service response
	expectedResponse := &aiassistant.DreamProgressAnalysisDTO{
		UserID: suite.testUserID,
		DreamsSummary: aiassistant.DreamsSummaryDTO{
			TotalDreams:                5,
			ActiveDreams:               3,
			CompletedDreams:            2,
			TotalTargetAmount:          150000,
			TotalSavedAmount:           45000,
			OverallProgress:            30.0,
			AverageMonthlyContribution: 1200,
		},
		ActiveDreams: []aiassistant.ActiveDreamDTO{
			{
				ID:                  "dream_123",
				Name:                "Casa Própria",
				Category:            "Familial",
				TargetAmount:        100000,
				SavedAmount:         25000,
				ProgressPercent:     25.0,
				MonthlyContribution: 2000,
				MonthsElapsed:       12,
				TargetDate:          "2027-03-15",
				CreatedDate:         "2024-01-15",
				FundingSource:       "salary_savings",
				IsShared:            true,
				ContributorsCount:   2,
				Timeframe:           "long_term",
			},
		},
		ProgressMetrics: aiassistant.ProgressMetricsDTO{
			DreamsOnSchedule:       2,
			DreamsBehindSchedule:   1,
			AverageProgressRate:    8.5,
			TotalMonthlyCommitment: 3500,
			CompletionRate:         40.0,
		},
		ContributionHistory: []aiassistant.ContributionHistoryDTO{
			{
				Month:              "2024-12",
				TotalContributions: 2000,
				DreamID:            "dream_123",
				Amount:             2000,
			},
		},
	}

	suite.mockService.On("DreamProgressAnalysis", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/dream-progress-analysis", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Call handler
	handler := suite.controller.DreamProgressAnalysis()
	err = handler(c)

	// Verify results
	suite.Require().NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Parse response
	var response aiassistant.DreamProgressAnalysisDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)

	// Verify response content
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal(5, response.DreamsSummary.TotalDreams)
	suite.Equal(3, response.DreamsSummary.ActiveDreams)
	suite.Equal(2, response.DreamsSummary.CompletedDreams)
	suite.Equal(int64(150000), response.DreamsSummary.TotalTargetAmount)
	suite.Equal(int64(45000), response.DreamsSummary.TotalSavedAmount)
	suite.Equal(30.0, response.DreamsSummary.OverallProgress)

	suite.Len(response.ActiveDreams, 1)
	suite.Equal("dream_123", response.ActiveDreams[0].ID)
	suite.Equal("Casa Própria", response.ActiveDreams[0].Name)
	suite.Equal("Familial", response.ActiveDreams[0].Category)
	suite.Equal(int64(100000), response.ActiveDreams[0].TargetAmount)
	suite.Equal(25.0, response.ActiveDreams[0].ProgressPercent)

	suite.Equal(2, response.ProgressMetrics.DreamsOnSchedule)
	suite.Equal(1, response.ProgressMetrics.DreamsBehindSchedule)
	suite.Equal(8.5, response.ProgressMetrics.AverageProgressRate)

	suite.Len(response.ContributionHistory, 1)
	suite.Equal("2024-12", response.ContributionHistory[0].Month)
	suite.Equal(int64(2000), response.ContributionHistory[0].Amount)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestLearningPathData_Success() {
	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Create mock learning path data
	mockLearningPathData := &aiassistant.LearningPathDataDTO{
		UserID: suite.testUserID,
		LearningProgress: aiassistant.LearningProgressDTO{
			CompletedTrails:       2,
			TotalTrails:           5,
			CompletedLessons:      15,
			TotalLessons:          25,
			CompletedChallenges:   3,
			TotalChallenges:       5,
			OverallProgress:       72.0,
			AverageCompletionTime: 25.5,
		},
		CurrentTrails: []aiassistant.CurrentTrailDTO{
			{
				TrailID:          "trail_456",
				Name:             "Financial Planning",
				Progress:         60.0,
				LessonsCompleted: 6,
				TotalLessons:     10,
				StartDate:        "2024-01-01",
				LastActivity:     "2024-01-15",
				Category:         "Financial Education",
			},
		},
		CompletedTrails: []aiassistant.CompletedTrailDTO{
			{
				TrailID:       "trail_123",
				Name:          "Basic Finance",
				CompletedDate: "2024-01-20",
				TotalTime:     120,
				Category:      "Financial Education",
			},
		},
		LearningPatterns: aiassistant.LearningPatternsDTO{
			AverageSessionDuration: 25.5,
			PreferredLearningDays:  []string{"Monday", "Wednesday", "Friday"},
			CompletionRate:         72.0,
			StreakDays:             7,
			LongestStreak:          15,
		},
		Achievements: aiassistant.AchievementsDTO{
			Earned: 3,
			Total:  10,
			RecentAchievements: []aiassistant.RecentAchievementDTO{
				{
					ID:          "dna",
					Name:        "Descobriu o DNA",
					Description: "Completed DNA trail",
					EarnedDate:  "2024-01-10",
					Icon:        "https://example.com/icon.png",
				},
			},
		},
	}

	// Set up mock expectation
	suite.mockService.On("LearningPathData", mock.Anything, suite.testUserID).Return(mockLearningPathData, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/learning-path-data", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Create response recorder
	rec := httptest.NewRecorder()

	// Create Echo context
	c := suite.echo.NewContext(req, rec)

	// Call the handler
	handler := suite.controller.LearningPathData()
	err = handler(c)

	// Assertions
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Parse response
	var response aiassistant.LearningPathDataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response data
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal(2, response.LearningProgress.CompletedTrails)
	suite.Equal(5, response.LearningProgress.TotalTrails)
	suite.Equal(15, response.LearningProgress.CompletedLessons)
	suite.Equal(25, response.LearningProgress.TotalLessons)
	suite.Equal(72.0, response.LearningProgress.OverallProgress)

	suite.Len(response.CurrentTrails, 1)
	suite.Equal("trail_456", response.CurrentTrails[0].TrailID)
	suite.Equal("Financial Planning", response.CurrentTrails[0].Name)
	suite.Equal(60.0, response.CurrentTrails[0].Progress)

	suite.Len(response.CompletedTrails, 1)
	suite.Equal("trail_123", response.CompletedTrails[0].TrailID)
	suite.Equal("Basic Finance", response.CompletedTrails[0].Name)

	suite.Equal(72.0, response.LearningPatterns.CompletionRate)
	suite.Equal(7, response.LearningPatterns.StreakDays)

	suite.Equal(3, response.Achievements.Earned)
	suite.Equal(10, response.Achievements.Total)
	suite.Len(response.Achievements.RecentAchievements, 1)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestSpendingAnalysisData_Success() {
	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Create mock spending analysis data
	mockSpendingAnalysisData := &aiassistant.SpendingAnalysisDataDTO{
		UserID: suite.testUserID,
		SpendingOverview: aiassistant.SpendingOverviewDTO{
			TotalMonthlyExpenses:   3500,
			FixedExpenses:          2000,
			VariableExpenses:       1200,
			DebtPayments:           300,
			ExpenseGrowthRate:      5.2,
			AverageTransactionSize: 87.50,
		},
		CategoryBreakdown: []aiassistant.CategoryBreakdownDTO{
			{
				Category:               "food",
				Amount:                 800,
				Percentage:             22.9,
				TransactionCount:       18,
				AverageTransactionSize: 44.44,
				MonthlyChange:          12.5,
				LastMonthAmount:        712,
				YearToDateAmount:       9600,
			},
		},
		SpendingPatterns: aiassistant.SpendingPatternsDTO{
			PeakSpendingDays:          []string{"Friday", "Saturday"},
			AverageTransactionsPerDay: 3.2,
			WeekendVsWeekdayRatio:     1.4,
			PeakSpendingHours:         []string{"12:00-14:00", "18:00-20:00"},
			SeasonalVariations: []aiassistant.SeasonalVariationDTO{
				{
					Month:         "December",
					Amount:        4200,
					ChangePercent: 20.0,
				},
			},
		},
		PaymentMethodBreakdown: []aiassistant.PaymentMethodBreakdownDTO{
			{
				Method:           "credit_card",
				Percentage:       65.0,
				Amount:           2275,
				TransactionCount: 19,
				AverageAmount:    119.74,
			},
		},
		HistoricalTrends: []aiassistant.SpendingHistoricalTrendDTO{
			{
				Month:            "2024-12",
				TotalExpenses:    3400,
				FixedExpenses:    2000,
				VariableExpenses: 1100,
				DebtPayments:     300,
			},
		},
	}

	// Set up mock expectation
	suite.mockService.On("SpendingAnalysisData", mock.Anything, suite.testUserID).Return(mockSpendingAnalysisData, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/spending-analysis-data", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Create response recorder
	rec := httptest.NewRecorder()

	// Create Echo context
	c := suite.echo.NewContext(req, rec)

	// Call the handler
	handler := suite.controller.SpendingAnalysisData()
	err = handler(c)

	// Assertions
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Parse response
	var response aiassistant.SpendingAnalysisDataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response data
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal(int64(3500), response.SpendingOverview.TotalMonthlyExpenses)
	suite.Equal(int64(2000), response.SpendingOverview.FixedExpenses)
	suite.Equal(int64(1200), response.SpendingOverview.VariableExpenses)
	suite.Equal(int64(300), response.SpendingOverview.DebtPayments)
	suite.Equal(5.2, response.SpendingOverview.ExpenseGrowthRate)
	suite.Equal(87.50, response.SpendingOverview.AverageTransactionSize)

	// Verify category breakdown
	suite.Len(response.CategoryBreakdown, 1)
	suite.Equal("food", response.CategoryBreakdown[0].Category)
	suite.Equal(int64(800), response.CategoryBreakdown[0].Amount)
	suite.Equal(22.9, response.CategoryBreakdown[0].Percentage)

	// Verify spending patterns
	suite.Equal([]string{"Friday", "Saturday"}, response.SpendingPatterns.PeakSpendingDays)
	suite.Equal(3.2, response.SpendingPatterns.AverageTransactionsPerDay)
	suite.Equal(1.4, response.SpendingPatterns.WeekendVsWeekdayRatio)

	// Verify payment method breakdown
	suite.Len(response.PaymentMethodBreakdown, 1)
	suite.Equal("credit_card", response.PaymentMethodBreakdown[0].Method)
	suite.Equal(65.0, response.PaymentMethodBreakdown[0].Percentage)

	// Verify historical trends
	suite.Len(response.HistoricalTrends, 1)
	suite.Equal("2024-12", response.HistoricalTrends[0].Month)
	suite.Equal(int64(3400), response.HistoricalTrends[0].TotalExpenses)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestInvestmentProfileData_Success() {
	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Create mock investment profile data
	mockInvestmentProfileData := &aiassistant.InvestmentProfileDataDTO{
		UserID: suite.testUserID,
		CurrentPortfolio: aiassistant.CurrentPortfolioDTO{
			TotalValue:           25000,
			MonthlyContributions: 800,
			InvestmentTypes: []aiassistant.InvestmentTypeDTO{
				{
					Type:       "stocks",
					Value:      15000,
					Percentage: 60.0,
				},
				{
					Type:       "bonds",
					Value:      7000,
					Percentage: 28.0,
				},
			},
			PortfolioAge: 18,
		},
		FinancialCapacity: aiassistant.FinancialCapacityDTO{
			MonthlyIncome:          5000,
			MonthlyExpenses:        3500,
			AvailableForInvestment: 1200,
			EmergencyFundMonths:    3.5,
			DebtToIncomeRatio:      0.15,
			SavingsRate:            30.0,
		},
		InvestmentKnowledge: aiassistant.InvestmentKnowledgeDTO{
			CompletedInvestmentTrails:   2,
			TotalInvestmentTrails:       5,
			InvestmentEducationProgress: 40.0,
			ChallengesCompleted:         3,
			LastLearningActivity:        "2025-01-05",
		},
		InvestmentBehavior: aiassistant.InvestmentBehaviorDTO{
			AverageMonthlyInvestment: 800,
			InvestmentConsistency:    85.0,
			MonthsInvesting:          18,
			LargestSingleInvestment:  2000,
			InvestmentFrequency:      "monthly",
		},
		RiskFactors: aiassistant.RiskFactorsDTO{
			AgeRange:          "25-35",
			TimeHorizon:       "long_term",
			Dependents:        0,
			JobStability:      "stable",
			IncomeVariability: "low",
		},
	}

	// Set up mock expectation
	suite.mockService.On("InvestmentProfileData", mock.Anything, suite.testUserID).Return(mockInvestmentProfileData, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/investment-profile-data", nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Create response recorder
	rec := httptest.NewRecorder()

	// Create Echo context
	c := suite.echo.NewContext(req, rec)

	// Call the handler
	handler := suite.controller.InvestmentProfileData()
	err = handler(c)

	// Assertions
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Parse response
	var response aiassistant.InvestmentProfileDataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response data
	suite.Equal(suite.testUserID, response.UserID)

	// Verify current portfolio
	suite.Equal(int64(25000), response.CurrentPortfolio.TotalValue)
	suite.Equal(int64(800), response.CurrentPortfolio.MonthlyContributions)
	suite.Equal(18, response.CurrentPortfolio.PortfolioAge)
	suite.Len(response.CurrentPortfolio.InvestmentTypes, 2)
	suite.Equal("stocks", response.CurrentPortfolio.InvestmentTypes[0].Type)
	suite.Equal(int64(15000), response.CurrentPortfolio.InvestmentTypes[0].Value)
	suite.Equal(60.0, response.CurrentPortfolio.InvestmentTypes[0].Percentage)

	// Verify financial capacity
	suite.Equal(int64(5000), response.FinancialCapacity.MonthlyIncome)
	suite.Equal(int64(3500), response.FinancialCapacity.MonthlyExpenses)
	suite.Equal(int64(1200), response.FinancialCapacity.AvailableForInvestment)
	suite.Equal(3.5, response.FinancialCapacity.EmergencyFundMonths)
	suite.Equal(0.15, response.FinancialCapacity.DebtToIncomeRatio)
	suite.Equal(30.0, response.FinancialCapacity.SavingsRate)

	// Verify investment knowledge
	suite.Equal(2, response.InvestmentKnowledge.CompletedInvestmentTrails)
	suite.Equal(5, response.InvestmentKnowledge.TotalInvestmentTrails)
	suite.Equal(40.0, response.InvestmentKnowledge.InvestmentEducationProgress)
	suite.Equal(3, response.InvestmentKnowledge.ChallengesCompleted)
	suite.Equal("2025-01-05", response.InvestmentKnowledge.LastLearningActivity)

	// Verify investment behavior
	suite.Equal(int64(800), response.InvestmentBehavior.AverageMonthlyInvestment)
	suite.Equal(85.0, response.InvestmentBehavior.InvestmentConsistency)
	suite.Equal(18, response.InvestmentBehavior.MonthsInvesting)
	suite.Equal(int64(2000), response.InvestmentBehavior.LargestSingleInvestment)
	suite.Equal("monthly", response.InvestmentBehavior.InvestmentFrequency)

	// Verify risk factors
	suite.Equal("25-35", response.RiskFactors.AgeRange)
	suite.Equal("long_term", response.RiskFactors.TimeHorizon)
	suite.Equal(0, response.RiskFactors.Dependents)
	suite.Equal("stable", response.RiskFactors.JobStability)
	suite.Equal("low", response.RiskFactors.IncomeVariability)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestFinancialIndependenceData_Success() {
	// Arrange
	expectedResponse := &aiassistant.FinancialIndependenceDataDTO{
		UserID: suite.testUserID,
		CurrentMetrics: aiassistant.CurrentMetricsDTO{
			NetWorth:             125000,
			MonthlyExpenses:      3500,
			MonthlyInvestments:   1000,
			MonthlyIncome:        5000,
			SavingsRate:          28.6,
			InvestmentGrowthRate: 8.5,
			YearsInvesting:       3.5,
		},
		FICalculations: aiassistant.FICalculationsDTO{
			LeanFiTarget:       750000,
			StandardFiTarget:   1050000,
			FatFiTarget:        1400000,
			CurrentFiProgress:  11.9,
			MonthsToLeanFi:     147,
			MonthsToStandardFi: 183,
			MonthsToFatFi:      225,
		},
		MilestoneTracking: []aiassistant.MilestoneTrackingDTO{
			{
				Milestone:       "emergency_fund_complete",
				TargetAmount:    21000,
				CurrentAmount:   18000,
				ProgressPercent: 85.7,
				MonthsRemaining: 2,
			},
			{
				Milestone:       "first_100k",
				TargetAmount:    100000,
				CurrentAmount:   125000,
				ProgressPercent: 100.0,
				CompletedDate:   "2024-08-15",
			},
		},
		HistoricalProgress: []aiassistant.HistoricalProgressDTO{
			{
				Date:               "2024-12-01",
				NetWorth:           120000,
				MonthlyInvestments: 950,
				FiProgress:         11.4,
			},
		},
		RetirementProjections: aiassistant.RetirementProjectionsDTO{
			CurrentAge:                    28,
			TargetRetirementAge:           45,
			YearsToRetirement:             17,
			ProjectedNetWorthAtRetirement: 1200000,
			RequiredMonthlyInvestment:     1000,
		},
	}

	suite.mockService.On("FinancialIndependenceData", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Act
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/financial-independence-data", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.FinancialIndependenceData()
	err = handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response aiassistant.FinancialIndependenceDataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response structure
	suite.Equal(suite.testUserID, response.UserID)

	// Verify current metrics
	suite.Equal(int64(125000), response.CurrentMetrics.NetWorth)
	suite.Equal(int64(3500), response.CurrentMetrics.MonthlyExpenses)
	suite.Equal(int64(1000), response.CurrentMetrics.MonthlyInvestments)
	suite.Equal(int64(5000), response.CurrentMetrics.MonthlyIncome)
	suite.Equal(28.6, response.CurrentMetrics.SavingsRate)
	suite.Equal(8.5, response.CurrentMetrics.InvestmentGrowthRate)
	suite.Equal(3.5, response.CurrentMetrics.YearsInvesting)

	// Verify FI calculations
	suite.Equal(int64(750000), response.FICalculations.LeanFiTarget)
	suite.Equal(int64(1050000), response.FICalculations.StandardFiTarget)
	suite.Equal(int64(1400000), response.FICalculations.FatFiTarget)
	suite.Equal(11.9, response.FICalculations.CurrentFiProgress)
	suite.Equal(147, response.FICalculations.MonthsToLeanFi)
	suite.Equal(183, response.FICalculations.MonthsToStandardFi)
	suite.Equal(225, response.FICalculations.MonthsToFatFi)

	// Verify milestone tracking
	suite.Len(response.MilestoneTracking, 2)
	suite.Equal("emergency_fund_complete", response.MilestoneTracking[0].Milestone)
	suite.Equal(int64(21000), response.MilestoneTracking[0].TargetAmount)
	suite.Equal(int64(18000), response.MilestoneTracking[0].CurrentAmount)
	suite.Equal(85.7, response.MilestoneTracking[0].ProgressPercent)
	suite.Equal(2, response.MilestoneTracking[0].MonthsRemaining)

	// Verify retirement projections
	suite.Equal(28, response.RetirementProjections.CurrentAge)
	suite.Equal(45, response.RetirementProjections.TargetRetirementAge)
	suite.Equal(17, response.RetirementProjections.YearsToRetirement)
	suite.Equal(int64(1200000), response.RetirementProjections.ProjectedNetWorthAtRetirement)
	suite.Equal(int64(1000), response.RetirementProjections.RequiredMonthlyInvestment)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestGamificationEngagementData_Success() {
	// Arrange
	expectedResponse := &aiassistant.GamificationEngagementDataDTO{
		UserID: suite.testUserID,
		EngagementMetrics: aiassistant.EngagementMetricsDTO{
			CurrentStreak:          15,
			LongestStreak:          25,
			TotalActiveDays:        120,
			WeeklyActiveHours:      8.5,
			LastActivityDate:       "2025-01-06T10:30:00Z",
			AverageSessionDuration: 25.5,
			MonthlyActiveHours:     34.0,
		},
		AchievementData: aiassistant.AchievementDataDTO{
			TotalEarned:    8,
			TotalAvailable: 15,
			CompletionRate: 53.3,
			RecentAchievements: []aiassistant.RecentAchievementDTO{
				{
					ID:          "dna",
					Name:        "DNA Descoberto",
					Description: "Descobriu seu DNA financeiro",
					Icon:        "https://images.dinbora.com.br/conquitas/dna.png",
					EarnedDate:  "2025-01-05T14:20:00Z",
				},
			},
			CategoryProgress: map[string]int{
				"financial_planning": 3,
				"investment":         2,
				"budgeting":          4,
				"education":          3,
			},
		},
		LeagueData: aiassistant.LeagueDataDTO{
			CurrentLeague:     "Silver",
			Position:          3,
			TotalMembers:      15,
			Points:            1250,
			MonthlyPoints:     350,
			TransactionStreak: 15,
			LeagueHistory: []aiassistant.LeagueHistoryDTO{
				{
					Month:    "2024-12",
					League:   "Bronze",
					Position: 2,
					Points:   900,
				},
			},
		},
		VaultData: aiassistant.VaultDataDTO{
			CurrentCoins:    850,
			TotalEarned:     2400,
			TotalSpent:      1550,
			MonthlyEarned:   200,
			LastEarningDate: "2025-01-06",
			EarningHistory: []aiassistant.EarningHistoryDTO{
				{
					Date:   "2025-01-05",
					Amount: 50,
					Source: "lesson_completion",
				},
			},
		},
	}

	suite.mockService.On("GamificationEngagementData", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Act
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/gamification-engagement-data", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.GamificationEngagementData()
	err = handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response aiassistant.GamificationEngagementDataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response structure
	suite.Equal(suite.testUserID, response.UserID)

	// Verify engagement metrics
	suite.Equal(15, response.EngagementMetrics.CurrentStreak)
	suite.Equal(25, response.EngagementMetrics.LongestStreak)
	suite.Equal(120, response.EngagementMetrics.TotalActiveDays)
	suite.Equal(8.5, response.EngagementMetrics.WeeklyActiveHours)
	suite.Equal("2025-01-06T10:30:00Z", response.EngagementMetrics.LastActivityDate)
	suite.Equal(25.5, response.EngagementMetrics.AverageSessionDuration)
	suite.Equal(34.0, response.EngagementMetrics.MonthlyActiveHours)

	// Verify achievement data
	suite.Equal(8, response.AchievementData.TotalEarned)
	suite.Equal(15, response.AchievementData.TotalAvailable)
	suite.Equal(53.3, response.AchievementData.CompletionRate)
	suite.Len(response.AchievementData.RecentAchievements, 1)
	suite.Equal("dna", response.AchievementData.RecentAchievements[0].ID)
	suite.Equal("DNA Descoberto", response.AchievementData.RecentAchievements[0].Name)

	// Verify league data
	suite.Equal("Silver", response.LeagueData.CurrentLeague)
	suite.Equal(3, response.LeagueData.Position)
	suite.Equal(15, response.LeagueData.TotalMembers)
	suite.Equal(1250, response.LeagueData.Points)
	suite.Equal(350, response.LeagueData.MonthlyPoints)
	suite.Equal(15, response.LeagueData.TransactionStreak)

	// Verify vault data
	suite.Equal(850, response.VaultData.CurrentCoins)
	suite.Equal(2400, response.VaultData.TotalEarned)
	suite.Equal(1550, response.VaultData.TotalSpent)
	suite.Equal(200, response.VaultData.MonthlyEarned)
	suite.Equal("2025-01-06", response.VaultData.LastEarningDate)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestFamilyFinancialDNAData_Success() {
	// Arrange
	expectedResponse := &aiassistant.FamilyFinancialDNADataDTO{
		UserID: suite.testUserID,
		UserDNA: aiassistant.UserDNADTO{
			CurrentProfile:   "balanced",
			ProfileScore:     75.5,
			StrengthAreas:    []string{"budgeting", "goal_setting"},
			ImprovementAreas: []string{"investment_knowledge", "emergency_fund"},
			LastAssessment:   "2025-01-01T10:00:00Z",
		},
		FamilyComparison: aiassistant.FamilyComparisonDTO{
			CommonTraits:        []string{"goal_oriented", "conservative"},
			DiversityIndex:      0.65,
			CollectiveStrength:  "Strong financial planning foundation",
			FamilyGoalAlignment: 82.3,
		},
		DNAEvolution: aiassistant.DNAEvolutionDTO{
			EvolutionTrend:    "improving",
			StabilityScore:    72.5,
			NextAssessmentDue: "2025-03-01",
		},
	}

	suite.mockService.On("FamilyFinancialDNAData", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Act
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/family-financial-dna-data", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.FamilyFinancialDNAData()
	err = handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response aiassistant.FamilyFinancialDNADataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response structure
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal("balanced", response.UserDNA.CurrentProfile)
	suite.Equal(75.5, response.UserDNA.ProfileScore)
	suite.Equal("improving", response.DNAEvolution.EvolutionTrend)
	suite.Equal(72.5, response.DNAEvolution.StabilityScore)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestTransactionBehaviorData_Success() {
	// Arrange
	expectedResponse := &aiassistant.TransactionBehaviorDataDTO{
		UserID: suite.testUserID,
		TransactionPatterns: aiassistant.TransactionPatternsDTO{
			AverageTransactionValue: 125.50,
			TransactionFrequency:    "daily",
			MostActiveDay:           "Friday",
			MostActiveTimeOfDay:     "evening",
			ConsistencyScore:        78.5,
			ImpulseTransactionRate:  15.3,
		},
		SpendingBehavior: aiassistant.SpendingBehaviorDTO{
			MonthlySpendingTrend:    "increasing",
			BudgetAdherence:         82.3,
			EmergencySpendingRate:   8.7,
			PlannedVsUnplannedRatio: 75.2,
		},
		CategoryAnalysis: aiassistant.CategoryAnalysisDTO{
			TopCategories: []aiassistant.CategorySpendingDTO{
				{
					Category:         "food",
					Amount:           1250.00,
					Percentage:       35.2,
					TransactionCount: 45,
				},
			},
		},
	}

	suite.mockService.On("TransactionBehaviorData", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Act
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/transaction-behavior-data", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.TransactionBehaviorData()
	err = handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response aiassistant.TransactionBehaviorDataDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response structure
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal(125.50, response.TransactionPatterns.AverageTransactionValue)
	suite.Equal("daily", response.TransactionPatterns.TransactionFrequency)
	suite.Equal("Friday", response.TransactionPatterns.MostActiveDay)
	suite.Equal("increasing", response.SpendingBehavior.MonthlySpendingTrend)
	suite.Equal(82.3, response.SpendingBehavior.BudgetAdherence)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}

func (suite *AIAssistantControllerTestSuite) TestComprehensiveFinancialContext_Success() {
	// Arrange
	expectedResponse := &aiassistant.ComprehensiveFinancialContextDTO{
		UserID: suite.testUserID,
		FinancialSnapshot: aiassistant.FinancialSnapshotDTO{
			NetWorth:             50000.00,
			MonthlyIncome:        5000.00,
			MonthlyExpenses:      3500.00,
			SavingsRate:          30.0,
			DebtToIncomeRatio:    0.25,
			EmergencyFundMonths:  6.0,
			FinancialHealthScore: 78.5,
		},
		GoalsAndDreams: aiassistant.GoalsAndDreamsDTO{
			GoalProgressOverview: aiassistant.GoalProgressOverviewDTO{
				TotalGoals:        3,
				CompletedGoals:    1,
				OnTrackGoals:      2,
				BehindSchedule:    0,
				AverageProgress:   65.5,
				TotalTargetAmount: 50000.00,
				TotalSavedAmount:  32500.00,
			},
		},
	}

	suite.mockService.On("ComprehensiveFinancialContext", mock.Anything, suite.testUserID).Return(expectedResponse, nil)

	// Create test JWT
	accessToken, err := suite.createTestJWT()
	suite.Require().NoError(err)

	// Act
	req := httptest.NewRequest(http.MethodGet, "/v2/aiassistant/comprehensive-financial-context", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.ComprehensiveFinancialContext()
	err = handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response aiassistant.ComprehensiveFinancialContextDTO
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)

	// Verify response structure
	suite.Equal(suite.testUserID, response.UserID)
	suite.Equal(50000.00, response.FinancialSnapshot.NetWorth)
	suite.Equal(5000.00, response.FinancialSnapshot.MonthlyIncome)
	suite.Equal(3500.00, response.FinancialSnapshot.MonthlyExpenses)
	suite.Equal(30.0, response.FinancialSnapshot.SavingsRate)
	suite.Equal(3, response.GoalsAndDreams.GoalProgressOverview.TotalGoals)

	// Verify mock expectations
	suite.mockService.AssertExpectations(suite.T())
}
