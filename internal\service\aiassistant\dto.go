package aiassistant

// FinancialHealthOverviewDTO represents the response for the Financial Health Overview API
type FinancialHealthOverviewDTO struct {
	UserID            string               `json:"userId"`
	MonthlyFinancials MonthlyFinancialsDTO `json:"monthlyFinancials"`
	StressMetrics     StressMetricsDTO     `json:"stressMetrics"`
	NetWorthData      NetWorthDataDTO      `json:"netWorthData"`
	HistoricalTrends  []HistoricalTrendDTO `json:"historicalTrends"`
	DataTimestamp     string               `json:"dataTimestamp"`
}

// MonthlyFinancialsDTO contains monthly financial data
type MonthlyFinancialsDTO struct {
	Income           int64   `json:"income"`
	Expenses         int64   `json:"expenses"`
	Savings          int64   `json:"savings"`
	SavingsRate      float64 `json:"savingsRate"`
	FixedExpenses    int64   `json:"fixedExpenses"`
	VariableExpenses int64   `json:"variableExpenses"`
	DebtPayments     int64   `json:"debtPayments"`
}

// StressMetricsDTO contains financial stress metrics
type StressMetricsDTO struct {
	CommitmentRatio     float64 `json:"commitmentRatio"`
	EmergencyFundMonths float64 `json:"emergencyFundMonths"`
	DebtToIncomeRatio   float64 `json:"debtToIncomeRatio"`
	ExpenseGrowthRate   float64 `json:"expenseGrowthRate"`
}

// NetWorthDataDTO contains net worth information
type NetWorthDataDTO struct {
	Current       int64   `json:"current"`
	PreviousMonth int64   `json:"previousMonth"`
	ChangePercent float64 `json:"changePercent"`
	StrategicFund int64   `json:"strategicFund"`
	Investments   int64   `json:"investments"`
	Assets        int64   `json:"assets"`
	Debts         int64   `json:"debts"`
}

// HistoricalTrendDTO contains historical trend data
type HistoricalTrendDTO struct {
	Month    string `json:"month"`
	NetWorth int64  `json:"netWorth"`
	Income   int64  `json:"income"`
	Expenses int64  `json:"expenses"`
}

// DreamProgressAnalysisDTO represents the response for the Dream Progress Analysis API
type DreamProgressAnalysisDTO struct {
	UserID              string                   `json:"userId"`
	DreamsSummary       DreamsSummaryDTO         `json:"dreamsSummary"`
	ActiveDreams        []ActiveDreamDTO         `json:"activeDreams"`
	ProgressMetrics     ProgressMetricsDTO       `json:"progressMetrics"`
	ContributionHistory []ContributionHistoryDTO `json:"contributionHistory"`
}

// DreamsSummaryDTO contains overall dreams summary data
type DreamsSummaryDTO struct {
	TotalDreams                int     `json:"totalDreams"`
	ActiveDreams               int     `json:"activeDreams"`
	CompletedDreams            int     `json:"completedDreams"`
	TotalTargetAmount          int64   `json:"totalTargetAmount"`
	TotalSavedAmount           int64   `json:"totalSavedAmount"`
	OverallProgress            float64 `json:"overallProgress"`
	AverageMonthlyContribution int64   `json:"averageMonthlyContribution"`
}

// ActiveDreamDTO contains information about an active dream
type ActiveDreamDTO struct {
	ID                  string  `json:"id"`
	Name                string  `json:"name"`
	Category            string  `json:"category"`
	TargetAmount        int64   `json:"targetAmount"`
	SavedAmount         int64   `json:"savedAmount"`
	ProgressPercent     float64 `json:"progressPercent"`
	MonthlyContribution int64   `json:"monthlyContribution"`
	MonthsElapsed       int     `json:"monthsElapsed"`
	TargetDate          string  `json:"targetDate"`
	CreatedDate         string  `json:"createdDate"`
	FundingSource       string  `json:"fundingSource"`
	IsShared            bool    `json:"isShared"`
	ContributorsCount   int     `json:"contributorsCount"`
	Timeframe           string  `json:"timeframe"`
}

// ProgressMetricsDTO contains progress analysis metrics
type ProgressMetricsDTO struct {
	DreamsOnSchedule       int     `json:"dreamsOnSchedule"`
	DreamsBehindSchedule   int     `json:"dreamsBehindSchedule"`
	AverageProgressRate    float64 `json:"averageProgressRate"`
	TotalMonthlyCommitment int64   `json:"totalMonthlyCommitment"`
	CompletionRate         float64 `json:"completionRate"`
}

// ContributionHistoryDTO contains historical contribution data
type ContributionHistoryDTO struct {
	Month              string `json:"month"`
	TotalContributions int64  `json:"totalContributions"`
	DreamID            string `json:"dreamId"`
	Amount             int64  `json:"amount"`
}

// LearningPathDataDTO represents the response for the Learning Path Data API
type LearningPathDataDTO struct {
	UserID           string              `json:"userId"`
	LearningProgress LearningProgressDTO `json:"learningProgress"`
	CurrentTrails    []CurrentTrailDTO   `json:"currentTrails"`
	CompletedTrails  []CompletedTrailDTO `json:"completedTrails"`
	LearningPatterns LearningPatternsDTO `json:"learningPatterns"`
	Achievements     AchievementsDTO     `json:"achievements"`
}

// LearningPatternsDTO contains learning behavior patterns
type LearningPatternsDTO struct {
	AverageSessionDuration float64  `json:"averageSessionDuration"`
	PreferredLearningDays  []string `json:"preferredLearningDays"`
	CompletionRate         float64  `json:"completionRate"`
	StreakDays             int      `json:"streakDays"`
	LongestStreak          int      `json:"longestStreak"`
}

// AchievementsDTO contains achievement data
type AchievementsDTO struct {
	Earned             int                    `json:"earned"`
	Total              int                    `json:"total"`
	RecentAchievements []RecentAchievementDTO `json:"recentAchievements"`
}

// RecentAchievementDTO contains information about a recent achievement
type RecentAchievementDTO struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	EarnedDate  string `json:"earnedDate"`
	Icon        string `json:"icon"`
}

// SpendingAnalysisDataDTO represents the response for the Spending Analysis Data API
type SpendingAnalysisDataDTO struct {
	UserID                 string                       `json:"userId"`
	SpendingOverview       SpendingOverviewDTO          `json:"spendingOverview"`
	CategoryBreakdown      []CategoryBreakdownDTO       `json:"categoryBreakdown"`
	SpendingPatterns       SpendingPatternsDTO          `json:"spendingPatterns"`
	PaymentMethodBreakdown []PaymentMethodBreakdownDTO  `json:"paymentMethodBreakdown"`
	HistoricalTrends       []SpendingHistoricalTrendDTO `json:"historicalTrends"`
}

// SpendingOverviewDTO contains overall spending overview data
type SpendingOverviewDTO struct {
	TotalMonthlyExpenses   int64   `json:"totalMonthlyExpenses"`
	FixedExpenses          int64   `json:"fixedExpenses"`
	VariableExpenses       int64   `json:"variableExpenses"`
	DebtPayments           int64   `json:"debtPayments"`
	ExpenseGrowthRate      float64 `json:"expenseGrowthRate"`
	AverageTransactionSize float64 `json:"averageTransactionSize"`
}

// CategoryBreakdownDTO contains spending breakdown by category
type CategoryBreakdownDTO struct {
	Category               string  `json:"category"`
	Amount                 int64   `json:"amount"`
	Percentage             float64 `json:"percentage"`
	TransactionCount       int     `json:"transactionCount"`
	AverageTransactionSize float64 `json:"averageTransactionSize"`
	MonthlyChange          float64 `json:"monthlyChange"`
	LastMonthAmount        int64   `json:"lastMonthAmount"`
	YearToDateAmount       int64   `json:"yearToDateAmount"`
}

// SeasonalVariationDTO contains seasonal spending variation data
type SeasonalVariationDTO struct {
	Month         string  `json:"month"`
	Amount        int64   `json:"amount"`
	ChangePercent float64 `json:"changePercent"`
}

// PaymentMethodBreakdownDTO contains payment method breakdown data
type PaymentMethodBreakdownDTO struct {
	Method           string  `json:"method"`
	Percentage       float64 `json:"percentage"`
	Amount           int64   `json:"amount"`
	TransactionCount int     `json:"transactionCount"`
	AverageAmount    float64 `json:"averageAmount"`
}

// SpendingHistoricalTrendDTO contains historical spending trend data
type SpendingHistoricalTrendDTO struct {
	Month            string `json:"month"`
	TotalExpenses    int64  `json:"totalExpenses"`
	FixedExpenses    int64  `json:"fixedExpenses"`
	VariableExpenses int64  `json:"variableExpenses"`
	DebtPayments     int64  `json:"debtPayments"`
}

// InvestmentProfileDataDTO represents the response for the Investment Profile Data API
type InvestmentProfileDataDTO struct {
	UserID              string                 `json:"userId"`
	CurrentPortfolio    CurrentPortfolioDTO    `json:"currentPortfolio"`
	FinancialCapacity   FinancialCapacityDTO   `json:"financialCapacity"`
	InvestmentKnowledge InvestmentKnowledgeDTO `json:"investmentKnowledge"`
	InvestmentBehavior  InvestmentBehaviorDTO  `json:"investmentBehavior"`
	RiskFactors         RiskFactorsDTO         `json:"riskFactors"`
}

// CurrentPortfolioDTO contains current investment portfolio data
type CurrentPortfolioDTO struct {
	TotalValue           int64               `json:"totalValue"`
	MonthlyContributions int64               `json:"monthlyContributions"`
	InvestmentTypes      []InvestmentTypeDTO `json:"investmentTypes"`
	PortfolioAge         int                 `json:"portfolioAge"`
}

// InvestmentTypeDTO contains investment type breakdown data
type InvestmentTypeDTO struct {
	Type       string  `json:"type"`
	Value      int64   `json:"value"`
	Percentage float64 `json:"percentage"`
}

// FinancialCapacityDTO contains financial capacity metrics
type FinancialCapacityDTO struct {
	MonthlyIncome          int64   `json:"monthlyIncome"`
	MonthlyExpenses        int64   `json:"monthlyExpenses"`
	AvailableForInvestment int64   `json:"availableForInvestment"`
	EmergencyFundMonths    float64 `json:"emergencyFundMonths"`
	DebtToIncomeRatio      float64 `json:"debtToIncomeRatio"`
	SavingsRate            float64 `json:"savingsRate"`
}

// InvestmentKnowledgeDTO contains investment education and knowledge data
type InvestmentKnowledgeDTO struct {
	CompletedInvestmentTrails   int     `json:"completedInvestmentTrails"`
	TotalInvestmentTrails       int     `json:"totalInvestmentTrails"`
	InvestmentEducationProgress float64 `json:"investmentEducationProgress"`
	ChallengesCompleted         int     `json:"challengesCompleted"`
	LastLearningActivity        string  `json:"lastLearningActivity"`
}

// InvestmentBehaviorDTO contains investment behavior patterns
type InvestmentBehaviorDTO struct {
	AverageMonthlyInvestment int64   `json:"averageMonthlyInvestment"`
	InvestmentConsistency    float64 `json:"investmentConsistency"`
	MonthsInvesting          int     `json:"monthsInvesting"`
	LargestSingleInvestment  int64   `json:"largestSingleInvestment"`
	InvestmentFrequency      string  `json:"investmentFrequency"`
}

// RiskFactorsDTO contains risk assessment factors
type RiskFactorsDTO struct {
	AgeRange          string `json:"ageRange"`
	TimeHorizon       string `json:"timeHorizon"`
	Dependents        int    `json:"dependents"`
	JobStability      string `json:"jobStability"`
	IncomeVariability string `json:"incomeVariability"`
}

// FinancialIndependenceDataDTO represents the response for the Financial Independence Data API
type FinancialIndependenceDataDTO struct {
	UserID                string                   `json:"userId"`
	CurrentMetrics        CurrentMetricsDTO        `json:"currentMetrics"`
	FICalculations        FICalculationsDTO        `json:"fiCalculations"`
	MilestoneTracking     []MilestoneTrackingDTO   `json:"milestoneTracking"`
	HistoricalProgress    []HistoricalProgressDTO  `json:"historicalProgress"`
	RetirementProjections RetirementProjectionsDTO `json:"retirementProjections"`
}

// CurrentMetricsDTO contains current financial independence metrics
type CurrentMetricsDTO struct {
	NetWorth             int64   `json:"netWorth"`
	MonthlyExpenses      int64   `json:"monthlyExpenses"`
	MonthlyInvestments   int64   `json:"monthlyInvestments"`
	MonthlyIncome        int64   `json:"monthlyIncome"`
	SavingsRate          float64 `json:"savingsRate"`
	InvestmentGrowthRate float64 `json:"investmentGrowthRate"`
	YearsInvesting       float64 `json:"yearsInvesting"`
}

// FICalculationsDTO contains financial independence calculations
type FICalculationsDTO struct {
	LeanFiTarget       int64   `json:"leanFiTarget"`
	StandardFiTarget   int64   `json:"standardFiTarget"`
	FatFiTarget        int64   `json:"fatFiTarget"`
	CurrentFiProgress  float64 `json:"currentFiProgress"`
	MonthsToLeanFi     int     `json:"monthsToLeanFi"`
	MonthsToStandardFi int     `json:"monthsToStandardFi"`
	MonthsToFatFi      int     `json:"monthsToFatFi"`
}

// MilestoneTrackingDTO contains milestone tracking data
type MilestoneTrackingDTO struct {
	Milestone       string  `json:"milestone"`
	TargetAmount    int64   `json:"targetAmount"`
	CurrentAmount   int64   `json:"currentAmount"`
	ProgressPercent float64 `json:"progressPercent"`
	MonthsRemaining int     `json:"monthsRemaining,omitempty"`
	CompletedDate   string  `json:"completedDate,omitempty"`
}

// HistoricalProgressDTO contains historical FI progress data
type HistoricalProgressDTO struct {
	Date               string  `json:"date"`
	NetWorth           int64   `json:"netWorth"`
	MonthlyInvestments int64   `json:"monthlyInvestments"`
	FiProgress         float64 `json:"fiProgress"`
}

// RetirementProjectionsDTO contains retirement projection data
type RetirementProjectionsDTO struct {
	CurrentAge                    int   `json:"currentAge"`
	TargetRetirementAge           int   `json:"targetRetirementAge"`
	YearsToRetirement             int   `json:"yearsToRetirement"`
	ProjectedNetWorthAtRetirement int64 `json:"projectedNetWorthAtRetirement"`
	RequiredMonthlyInvestment     int64 `json:"requiredMonthlyInvestment"`
}

// GamificationEngagementDataDTO represents the response for the Gamification Engagement Data API
type GamificationEngagementDataDTO struct {
	UserID            string               `json:"userId"`
	EngagementMetrics EngagementMetricsDTO `json:"engagementMetrics"`
	AchievementData   AchievementDataDTO   `json:"achievementData"`
	LeagueData        LeagueDataDTO        `json:"leagueData"`
	VaultData         VaultDataDTO         `json:"vaultData"`
}

// EngagementMetricsDTO contains user engagement metrics
type EngagementMetricsDTO struct {
	CurrentStreak          int     `json:"currentStreak"`
	LongestStreak          int     `json:"longestStreak"`
	TotalActiveDays        int     `json:"totalActiveDays"`
	WeeklyActiveHours      float64 `json:"weeklyActiveHours"`
	LastActivityDate       string  `json:"lastActivityDate"`
	AverageSessionDuration float64 `json:"averageSessionDuration"`
	MonthlyActiveHours     float64 `json:"monthlyActiveHours"`
}

// AchievementDataDTO contains achievement information
type AchievementDataDTO struct {
	TotalEarned        int                    `json:"totalEarned"`
	TotalAvailable     int                    `json:"totalAvailable"`
	CompletionRate     float64                `json:"completionRate"`
	RecentAchievements []RecentAchievementDTO `json:"recentAchievements"`
	CategoryProgress   map[string]int         `json:"categoryProgress"`
}

// LeagueDataDTO contains league participation data
type LeagueDataDTO struct {
	CurrentLeague     string             `json:"currentLeague"`
	Position          int                `json:"position"`
	TotalMembers      int                `json:"totalMembers"`
	Points            int                `json:"points"`
	MonthlyPoints     int                `json:"monthlyPoints"`
	TransactionStreak int                `json:"transactionStreak"`
	LeagueHistory     []LeagueHistoryDTO `json:"leagueHistory"`
}

// LeagueHistoryDTO contains historical league data
type LeagueHistoryDTO struct {
	Month    string `json:"month"`
	League   string `json:"league"`
	Position int    `json:"position"`
	Points   int    `json:"points"`
}

// VaultDataDTO contains vault/coin information
type VaultDataDTO struct {
	CurrentCoins    int                 `json:"currentCoins"`
	TotalEarned     int                 `json:"totalEarned"`
	TotalSpent      int                 `json:"totalSpent"`
	MonthlyEarned   int                 `json:"monthlyEarned"`
	LastEarningDate string              `json:"lastEarningDate"`
	EarningHistory  []EarningHistoryDTO `json:"earningHistory"`
}

// EarningHistoryDTO contains coin earning history
type EarningHistoryDTO struct {
	Date   string `json:"date"`
	Amount int    `json:"amount"`
	Source string `json:"source"`
}

// FamilyFinancialDNADataDTO represents the response for the Family Financial DNA Data API
type FamilyFinancialDNADataDTO struct {
	UserID           string                 `json:"userId"`
	UserDNA          UserDNADTO             `json:"userDna"`
	FamilyComparison FamilyComparisonDTO    `json:"familyComparison"`
	DNAEvolution     DNAEvolutionDTO        `json:"dnaEvolution"`
	BehaviorInsights []BehaviorInsightDTO   `json:"behaviorInsights"`
	Recommendations  []DNARecommendationDTO `json:"recommendations"`
}

// UserDNADTO contains the user's current DNA profile
type UserDNADTO struct {
	CurrentProfile   string              `json:"currentProfile"`
	ProfileScore     float64             `json:"profileScore"`
	Characteristics  []CharacteristicDTO `json:"characteristics"`
	StrengthAreas    []string            `json:"strengthAreas"`
	ImprovementAreas []string            `json:"improvementAreas"`
	LastAssessment   string              `json:"lastAssessment"`
}

// CharacteristicDTO contains DNA characteristic information
type CharacteristicDTO struct {
	Name        string  `json:"name"`
	Score       float64 `json:"score"`
	Description string  `json:"description"`
	Category    string  `json:"category"`
}

// FamilyComparisonDTO contains family DNA comparison data
type FamilyComparisonDTO struct {
	FamilyMembers       []FamilyMemberDNADTO `json:"familyMembers"`
	CommonTraits        []string             `json:"commonTraits"`
	DiversityIndex      float64              `json:"diversityIndex"`
	CollectiveStrength  string               `json:"collectiveStrength"`
	FamilyGoalAlignment float64              `json:"familyGoalAlignment"`
}

// FamilyMemberDNADTO contains family member DNA information
type FamilyMemberDNADTO struct {
	UserID       string  `json:"userId"`
	Name         string  `json:"name"`
	Relationship string  `json:"relationship"`
	Profile      string  `json:"profile"`
	Similarity   float64 `json:"similarity"`
}

// DNAEvolutionDTO contains DNA evolution over time
type DNAEvolutionDTO struct {
	HistoricalProfiles []HistoricalProfileDTO `json:"historicalProfiles"`
	EvolutionTrend     string                 `json:"evolutionTrend"`
	StabilityScore     float64                `json:"stabilityScore"`
	NextAssessmentDue  string                 `json:"nextAssessmentDue"`
}

// HistoricalProfileDTO contains historical DNA profile data
type HistoricalProfileDTO struct {
	Date    string  `json:"date"`
	Profile string  `json:"profile"`
	Score   float64 `json:"score"`
	Trigger string  `json:"trigger"`
}

// BehaviorInsightDTO contains behavioral insights
type BehaviorInsightDTO struct {
	Category    string   `json:"category"`
	Insight     string   `json:"insight"`
	Impact      string   `json:"impact"`
	Confidence  float64  `json:"confidence"`
	ActionItems []string `json:"actionItems"`
}

// DNARecommendationDTO contains DNA-based recommendations
type DNARecommendationDTO struct {
	Type            string   `json:"type"`
	Title           string   `json:"title"`
	Description     string   `json:"description"`
	Priority        string   `json:"priority"`
	Actions         []string `json:"actions"`
	ExpectedOutcome string   `json:"expectedOutcome"`
}

// TransactionBehaviorDataDTO represents the response for the Transaction Behavior Data API
type TransactionBehaviorDataDTO struct {
	UserID              string                      `json:"userId"`
	TransactionPatterns TransactionPatternsDTO      `json:"transactionPatterns"`
	SpendingBehavior    SpendingBehaviorDTO         `json:"spendingBehavior"`
	CategoryAnalysis    CategoryAnalysisDTO         `json:"categoryAnalysis"`
	TemporalAnalysis    TemporalAnalysisDTO         `json:"temporalAnalysis"`
	BehaviorInsights    []TransactionInsightDTO     `json:"behaviorInsights"`
	Recommendations     []BehaviorRecommendationDTO `json:"recommendations"`
}

// TransactionPatternsDTO contains transaction pattern analysis
type TransactionPatternsDTO struct {
	AverageTransactionValue float64                   `json:"averageTransactionValue"`
	TransactionFrequency    string                    `json:"transactionFrequency"`
	MostActiveDay           string                    `json:"mostActiveDay"`
	MostActiveTimeOfDay     string                    `json:"mostActiveTimeOfDay"`
	ConsistencyScore        float64                   `json:"consistencyScore"`
	RecurringTransactions   []RecurringTransactionDTO `json:"recurringTransactions"`
	ImpulseTransactionRate  float64                   `json:"impulseTransactionRate"`
}

// RecurringTransactionDTO contains recurring transaction information
type RecurringTransactionDTO struct {
	Description string  `json:"description"`
	Amount      float64 `json:"amount"`
	Frequency   string  `json:"frequency"`
	Category    string  `json:"category"`
	Confidence  float64 `json:"confidence"`
}

// SpendingBehaviorDTO contains spending behavior analysis
type SpendingBehaviorDTO struct {
	MonthlySpendingTrend     string                  `json:"monthlySpendingTrend"`
	SeasonalPatterns         []SeasonalPatternDTO    `json:"seasonalPatterns"`
	BudgetAdherence          float64                 `json:"budgetAdherence"`
	EmergencySpendingRate    float64                 `json:"emergencySpendingRate"`
	PlannedVsUnplannedRatio  float64                 `json:"plannedVsUnplannedRatio"`
	PaymentMethodPreferences []PaymentMethodUsageDTO `json:"paymentMethodPreferences"`
}

// SeasonalPatternDTO contains seasonal spending pattern information
type SeasonalPatternDTO struct {
	Period           string   `json:"period"`
	SpendingIncrease float64  `json:"spendingIncrease"`
	MainCategories   []string `json:"mainCategories"`
}

// PaymentMethodUsageDTO contains payment method usage information
type PaymentMethodUsageDTO struct {
	Method        string  `json:"method"`
	Percentage    float64 `json:"percentage"`
	AverageAmount float64 `json:"averageAmount"`
}

// CategoryAnalysisDTO contains category-based analysis
type CategoryAnalysisDTO struct {
	TopCategories       []CategorySpendingDTO `json:"topCategories"`
	CategoryTrends      []CategoryTrendDTO    `json:"categoryTrends"`
	CategoryConsistency map[string]float64    `json:"categoryConsistency"`
	UnusualSpending     []UnusualSpendingDTO  `json:"unusualSpending"`
}

// CategorySpendingDTO contains category spending information
type CategorySpendingDTO struct {
	Category         string  `json:"category"`
	Amount           float64 `json:"amount"`
	Percentage       float64 `json:"percentage"`
	TransactionCount int     `json:"transactionCount"`
}

// CategoryTrendDTO contains category trend information
type CategoryTrendDTO struct {
	Category string  `json:"category"`
	Trend    string  `json:"trend"`
	Change   float64 `json:"change"`
}

// UnusualSpendingDTO contains unusual spending information
type UnusualSpendingDTO struct {
	Date           string  `json:"date"`
	Amount         float64 `json:"amount"`
	Category       string  `json:"category"`
	Description    string  `json:"description"`
	DeviationScore float64 `json:"deviationScore"`
}

// TemporalAnalysisDTO contains temporal analysis of transactions
type TemporalAnalysisDTO struct {
	WeeklyPatterns  []WeeklyPatternDTO  `json:"weeklyPatterns"`
	MonthlyPatterns []MonthlyPatternDTO `json:"monthlyPatterns"`
	HourlyPatterns  []HourlyPatternDTO  `json:"hourlyPatterns"`
	StreakAnalysis  StreakAnalysisDTO   `json:"streakAnalysis"`
}

// WeeklyPatternDTO contains weekly pattern information
type WeeklyPatternDTO struct {
	DayOfWeek string  `json:"dayOfWeek"`
	Amount    float64 `json:"amount"`
	Count     int     `json:"count"`
}

// MonthlyPatternDTO contains monthly pattern information
type MonthlyPatternDTO struct {
	Month  string  `json:"month"`
	Amount float64 `json:"amount"`
	Count  int     `json:"count"`
}

// HourlyPatternDTO contains hourly pattern information
type HourlyPatternDTO struct {
	Hour   int     `json:"hour"`
	Amount float64 `json:"amount"`
	Count  int     `json:"count"`
}

// StreakAnalysisDTO contains streak analysis information
type StreakAnalysisDTO struct {
	CurrentStreak       int     `json:"currentStreak"`
	LongestStreak       int     `json:"longestStreak"`
	StreakConsistency   float64 `json:"streakConsistency"`
	LastTransactionDate string  `json:"lastTransactionDate"`
}

// TransactionInsightDTO contains transaction behavior insights
type TransactionInsightDTO struct {
	Type        string   `json:"type"`
	Insight     string   `json:"insight"`
	Impact      string   `json:"impact"`
	Confidence  float64  `json:"confidence"`
	Evidence    []string `json:"evidence"`
	Suggestions []string `json:"suggestions"`
}

// BehaviorRecommendationDTO contains behavior-based recommendations
type BehaviorRecommendationDTO struct {
	Type           string   `json:"type"`
	Title          string   `json:"title"`
	Description    string   `json:"description"`
	Priority       string   `json:"priority"`
	Actions        []string `json:"actions"`
	ExpectedImpact string   `json:"expectedImpact"`
}

// ComprehensiveFinancialContextDTO represents the response for the Comprehensive Financial Context API
type ComprehensiveFinancialContextDTO struct {
	UserID                    string                        `json:"userId"`
	FinancialSnapshot         FinancialSnapshotDTO          `json:"financialSnapshot"`
	GoalsAndDreams            GoalsAndDreamsDTO             `json:"goalsAndDreams"`
	LearningProgress          LearningProgressDTO           `json:"learningProgress"`
	BehaviorProfile           BehaviorProfileDTO            `json:"behaviorProfile"`
	RiskAssessment            RiskAssessmentDTO             `json:"riskAssessment"`
	OpportunityAnalysis       OpportunityAnalysisDTO        `json:"opportunityAnalysis"`
	PersonalizedInsights      []PersonalizedInsightDTO      `json:"personalizedInsights"`
	ActionableRecommendations []ActionableRecommendationDTO `json:"actionableRecommendations"`
}

// FinancialSnapshotDTO contains current financial status
type FinancialSnapshotDTO struct {
	NetWorth             float64         `json:"netWorth"`
	MonthlyIncome        float64         `json:"monthlyIncome"`
	MonthlyExpenses      float64         `json:"monthlyExpenses"`
	SavingsRate          float64         `json:"savingsRate"`
	DebtToIncomeRatio    float64         `json:"debtToIncomeRatio"`
	EmergencyFundMonths  float64         `json:"emergencyFundMonths"`
	InvestmentAllocation []AllocationDTO `json:"investmentAllocation"`
	FinancialHealthScore float64         `json:"financialHealthScore"`
}

// AllocationDTO contains investment allocation information
type AllocationDTO struct {
	Category   string  `json:"category"`
	Percentage float64 `json:"percentage"`
	Amount     float64 `json:"amount"`
}

// GoalsAndDreamsDTO contains goals and dreams information
type GoalsAndDreamsDTO struct {
	ActiveGoals          []ActiveGoalDTO         `json:"activeGoals"`
	CompletedGoals       []CompletedGoalDTO      `json:"completedGoals"`
	GoalProgressOverview GoalProgressOverviewDTO `json:"goalProgressOverview"`
	RecommendedGoals     []RecommendedGoalDTO    `json:"recommendedGoals"`
}

// ActiveGoalDTO contains active goal information
type ActiveGoalDTO struct {
	ID              string  `json:"id"`
	Title           string  `json:"title"`
	TargetAmount    float64 `json:"targetAmount"`
	CurrentAmount   float64 `json:"currentAmount"`
	ProgressPercent float64 `json:"progressPercent"`
	MonthsRemaining int     `json:"monthsRemaining"`
	MonthlyRequired float64 `json:"monthlyRequired"`
	Category        string  `json:"category"`
	Priority        string  `json:"priority"`
}

// CompletedGoalDTO contains completed goal information
type CompletedGoalDTO struct {
	ID              string  `json:"id"`
	Title           string  `json:"title"`
	CompletedAmount float64 `json:"completedAmount"`
	CompletionDate  string  `json:"completionDate"`
	TimeTaken       string  `json:"timeTaken"`
	Category        string  `json:"category"`
}

// GoalProgressOverviewDTO contains goal progress overview
type GoalProgressOverviewDTO struct {
	TotalGoals        int     `json:"totalGoals"`
	CompletedGoals    int     `json:"completedGoals"`
	OnTrackGoals      int     `json:"onTrackGoals"`
	BehindSchedule    int     `json:"behindSchedule"`
	AverageProgress   float64 `json:"averageProgress"`
	TotalTargetAmount float64 `json:"totalTargetAmount"`
	TotalSavedAmount  float64 `json:"totalSavedAmount"`
}

// RecommendedGoalDTO contains recommended goal information
type RecommendedGoalDTO struct {
	Title           string  `json:"title"`
	Description     string  `json:"description"`
	SuggestedAmount float64 `json:"suggestedAmount"`
	Priority        string  `json:"priority"`
	Reasoning       string  `json:"reasoning"`
	Category        string  `json:"category"`
}

// LearningProgressDTO contains learning progress information
type LearningProgressDTO struct {
	CompletedTrails   []CompletedTrailDTO     `json:"completedTrails"`
	CurrentTrails     []CurrentTrailDTO       `json:"currentTrails"`
	RecommendedTrails []RecommendedTrailDTO   `json:"recommendedTrails"`
	SkillAssessment   SkillAssessmentDTO      `json:"skillAssessment"`
	LearningStats     LearningStatsDTO        `json:"learningStats"`
	Achievements      []AchievementSummaryDTO `json:"achievements"`
}

// CompletedTrailDTO contains completed trail information
type CompletedTrailDTO struct {
	ID             string `json:"id"`
	Title          string `json:"title"`
	Category       string `json:"category"`
	CompletionDate string `json:"completionDate"`
	Score          int    `json:"score"`
	TimeSpent      string `json:"timeSpent"`
}

// CurrentTrailDTO contains current trail information
type CurrentTrailDTO struct {
	ID              string  `json:"id"`
	Title           string  `json:"title"`
	Category        string  `json:"category"`
	ProgressPercent float64 `json:"progressPercent"`
	EstimatedTime   string  `json:"estimatedTime"`
	LastActivity    string  `json:"lastActivity"`
}

// RecommendedTrailDTO contains recommended trail information
type RecommendedTrailDTO struct {
	ID         string `json:"id"`
	Title      string `json:"title"`
	Category   string `json:"category"`
	Difficulty string `json:"difficulty"`
	Duration   string `json:"duration"`
	Reasoning  string `json:"reasoning"`
	Priority   string `json:"priority"`
}

// SkillAssessmentDTO contains skill assessment information
type SkillAssessmentDTO struct {
	OverallLevel     string          `json:"overallLevel"`
	SkillBreakdown   []SkillLevelDTO `json:"skillBreakdown"`
	StrengthAreas    []string        `json:"strengthAreas"`
	ImprovementAreas []string        `json:"improvementAreas"`
	NextMilestone    string          `json:"nextMilestone"`
}

// SkillLevelDTO contains skill level information
type SkillLevelDTO struct {
	Skill    string  `json:"skill"`
	Level    string  `json:"level"`
	Score    float64 `json:"score"`
	MaxScore float64 `json:"maxScore"`
}

// LearningStatsDTO contains learning statistics
type LearningStatsDTO struct {
	TotalTrailsCompleted int     `json:"totalTrailsCompleted"`
	TotalTimeSpent       string  `json:"totalTimeSpent"`
	AverageScore         float64 `json:"averageScore"`
	CurrentStreak        int     `json:"currentStreak"`
	LongestStreak        int     `json:"longestStreak"`
	WeeklyGoalProgress   float64 `json:"weeklyGoalProgress"`
}

// AchievementSummaryDTO contains achievement summary information
type AchievementSummaryDTO struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Category    string `json:"category"`
	EarnedDate  string `json:"earnedDate"`
	Rarity      string `json:"rarity"`
}

// BehaviorProfileDTO contains behavior profile information
type BehaviorProfileDTO struct {
	FinancialPersonality string                 `json:"financialPersonality"`
	SpendingPatterns     SpendingPatternsDTO    `json:"spendingPatterns"`
	SavingBehavior       SavingBehaviorDTO      `json:"savingBehavior"`
	InvestmentStyle      InvestmentStyleDTO     `json:"investmentStyle"`
	DecisionMakingStyle  DecisionMakingStyleDTO `json:"decisionMakingStyle"`
	MotivationFactors    []MotivationFactorDTO  `json:"motivationFactors"`
}

// SpendingPatternsDTO contains spending patterns information
type SpendingPatternsDTO struct {
	PrimaryCategories   []string `json:"primaryCategories"`
	SpendingConsistency float64  `json:"spendingConsistency"`
	ImpulsePurchaseRate float64  `json:"impulsePurchaseRate"`
	BudgetAdherence     float64  `json:"budgetAdherence"`
	SeasonalVariations  []string `json:"seasonalVariations"`
}

// SavingBehaviorDTO contains saving behavior information
type SavingBehaviorDTO struct {
	SavingConsistency     float64 `json:"savingConsistency"`
	AutomationLevel       string  `json:"automationLevel"`
	GoalOrientedSaving    float64 `json:"goalOrientedSaving"`
	EmergencyFundPriority string  `json:"emergencyFundPriority"`
}

// InvestmentStyleDTO contains investment style information
type InvestmentStyleDTO struct {
	RiskTolerance        string  `json:"riskTolerance"`
	InvestmentHorizon    string  `json:"investmentHorizon"`
	DiversificationLevel float64 `json:"diversificationLevel"`
	ActiveVsPassive      string  `json:"activeVsPassive"`
}

// DecisionMakingStyleDTO contains decision making style information
type DecisionMakingStyleDTO struct {
	Style            string   `json:"style"`
	ResearchDepth    string   `json:"researchDepth"`
	InfluenceFactors []string `json:"influenceFactors"`
	DecisionSpeed    string   `json:"decisionSpeed"`
}

// MotivationFactorDTO contains motivation factor information
type MotivationFactorDTO struct {
	Factor      string  `json:"factor"`
	Importance  float64 `json:"importance"`
	Description string  `json:"description"`
}

// RiskAssessmentDTO contains risk assessment information
type RiskAssessmentDTO struct {
	OverallRiskLevel   string               `json:"overallRiskLevel"`
	RiskFactors        []RiskFactorDTO      `json:"riskFactors"`
	ProtectionLevel    float64              `json:"protectionLevel"`
	RecommendedActions []RiskMitigationDTO  `json:"recommendedActions"`
	InsuranceCoverage  InsuranceCoverageDTO `json:"insuranceCoverage"`
}

// RiskFactorDTO contains risk factor information
type RiskFactorDTO struct {
	Type        string  `json:"type"`
	Level       string  `json:"level"`
	Impact      float64 `json:"impact"`
	Description string  `json:"description"`
	Mitigation  string  `json:"mitigation"`
}

// RiskMitigationDTO contains risk mitigation information
type RiskMitigationDTO struct {
	Action   string  `json:"action"`
	Priority string  `json:"priority"`
	Impact   string  `json:"impact"`
	Timeline string  `json:"timeline"`
	Cost     float64 `json:"cost"`
}

// InsuranceCoverageDTO contains insurance coverage information
type InsuranceCoverageDTO struct {
	LifeInsurance       CoverageDetailDTO `json:"lifeInsurance"`
	HealthInsurance     CoverageDetailDTO `json:"healthInsurance"`
	PropertyInsurance   CoverageDetailDTO `json:"propertyInsurance"`
	DisabilityInsurance CoverageDetailDTO `json:"disabilityInsurance"`
}

// CoverageDetailDTO contains coverage detail information
type CoverageDetailDTO struct {
	HasCoverage    bool    `json:"hasCoverage"`
	Coverage       float64 `json:"coverage"`
	Adequacy       string  `json:"adequacy"`
	Recommendation string  `json:"recommendation"`
}

// OpportunityAnalysisDTO contains opportunity analysis information
type OpportunityAnalysisDTO struct {
	ImmediateOpportunities  []OpportunityDTO      `json:"immediateOpportunities"`
	MediumTermOpportunities []OpportunityDTO      `json:"mediumTermOpportunities"`
	LongTermOpportunities   []OpportunityDTO      `json:"longTermOpportunities"`
	OptimizationAreas       []OptimizationAreaDTO `json:"optimizationAreas"`
}

// OpportunityDTO contains opportunity information
type OpportunityDTO struct {
	Type            string  `json:"type"`
	Title           string  `json:"title"`
	Description     string  `json:"description"`
	PotentialImpact float64 `json:"potentialImpact"`
	EffortRequired  string  `json:"effortRequired"`
	Timeline        string  `json:"timeline"`
	Priority        string  `json:"priority"`
}

// OptimizationAreaDTO contains optimization area information
type OptimizationAreaDTO struct {
	Area           string   `json:"area"`
	CurrentState   string   `json:"currentState"`
	OptimalState   string   `json:"optimalState"`
	ImprovementGap float64  `json:"improvementGap"`
	ActionSteps    []string `json:"actionSteps"`
}

// PersonalizedInsightDTO contains personalized insight information
type PersonalizedInsightDTO struct {
	Type         string   `json:"type"`
	Title        string   `json:"title"`
	Insight      string   `json:"insight"`
	DataPoints   []string `json:"dataPoints"`
	Significance string   `json:"significance"`
	Confidence   float64  `json:"confidence"`
	Category     string   `json:"category"`
}

// ActionableRecommendationDTO contains actionable recommendation information
type ActionableRecommendationDTO struct {
	ID              string          `json:"id"`
	Type            string          `json:"type"`
	Title           string          `json:"title"`
	Description     string          `json:"description"`
	Priority        string          `json:"priority"`
	Difficulty      string          `json:"difficulty"`
	EstimatedImpact float64         `json:"estimatedImpact"`
	Timeline        string          `json:"timeline"`
	Prerequisites   []string        `json:"prerequisites"`
	Steps           []ActionStepDTO `json:"steps"`
	SuccessMetrics  []string        `json:"successMetrics"`
}

// ActionStepDTO contains action step information
type ActionStepDTO struct {
	StepNumber  int      `json:"stepNumber"`
	Description string   `json:"description"`
	Timeline    string   `json:"timeline"`
	Resources   []string `json:"resources"`
}
