package aiassistant

import (
	"context"
	"fmt"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	dashboardModel "github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	dreamboardModel "github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	financialdnaModel "github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	financialsheetModel "github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	gamificationModel "github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	progressionModel "github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/achievement"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
)

// Service defines the interface for the AI Assistant service
type Service interface {

	// FinancialHealthOverview provides comprehensive financial health data
	FinancialHealthOverview(ctx context.Context, userID string) (*FinancialHealthOverviewDTO, error)

	// DreamProgressAnalysis provides detailed financial goals data
	DreamProgressAnalysis(ctx context.Context, userID string) (*DreamProgressAnalysisDTO, error)

	// LearningPathData provides comprehensive learning path data
	LearningPathData(ctx context.Context, userID string) (*LearningPathDataDTO, error)

	// SpendingAnalysisData provides comprehensive spending analysis data
	SpendingAnalysisData(ctx context.Context, userID string) (*SpendingAnalysisDataDTO, error)

	// InvestmentProfileData provides comprehensive investment profile data
	InvestmentProfileData(ctx context.Context, userID string) (*InvestmentProfileDataDTO, error)

	// FinancialIndependenceData provides comprehensive financial independence data
	FinancialIndependenceData(ctx context.Context, userID string) (*FinancialIndependenceDataDTO, error)

	// GamificationEngagementData provides comprehensive gamification engagement data
	GamificationEngagementData(ctx context.Context, userID string) (*GamificationEngagementDataDTO, error)

	// FamilyFinancialDNAData provides comprehensive family financial DNA data
	FamilyFinancialDNAData(ctx context.Context, userID string) (*FamilyFinancialDNADataDTO, error)

	// TransactionBehaviorData provides comprehensive transaction behavior data
	TransactionBehaviorData(ctx context.Context, userID string) (*TransactionBehaviorDataDTO, error)

	// ComprehensiveFinancialContext provides comprehensive financial context data
	ComprehensiveFinancialContext(ctx context.Context, userID string) (*ComprehensiveFinancialContextDTO, error)
}

// service implements the Service interface
type service struct {
	UserService           user.Service
	DashboardService      dashboard.Service
	DreamboardService     dreamboard.Service
	FinancialDNAService   financialdna.Service
	FinancialSheetService financialsheet.Service
	ProgressionService    progression.Service
	VaultService          vault.Service
	WalletService         wallet.Service
	AchievementService    achievement.Service
	TrailService          trail.Service
	GamificationService   gamification.Service
}

// New creates a new instance of the AI Assistant service
func New(
	userService user.Service,
	dashboardService dashboard.Service,
	dreamboardService dreamboard.Service,
	financialDNAService financialdna.Service,
	financialSheetService financialsheet.Service,
	progressionService progression.Service,
	vaultService vault.Service,
	walletService wallet.Service,
	achievementService achievement.Service,
	trailService trail.Service,
	gamificationService gamification.Service,
) Service {
	return &service{
		UserService:           userService,
		DashboardService:      dashboardService,
		DreamboardService:     dreamboardService,
		FinancialDNAService:   financialDNAService,
		FinancialSheetService: financialSheetService,
		ProgressionService:    progressionService,
		VaultService:          vaultService,
		WalletService:         walletService,
		AchievementService:    achievementService,
		TrailService:          trailService,
		GamificationService:   gamificationService,
	}
}

// FinancialHealthOverview provides comprehensive financial health data
func (s *service) FinancialHealthOverview(ctx context.Context, userID string) (*FinancialHealthOverviewDTO, error) {
	// Get financial map for basic financial data
	financialMap, err := s.DashboardService.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get financial stress analysis for current month
	// Possibilities: "7d", "30d", "90d", "1y", in future implement in this function a way to define the period
	financialStress, err := s.DashboardService.FindFinancialStress(ctx, userID, "30d")
	if err != nil {
		return nil, err
	}

	// Get net worth history for trend analysis
	netWorthHistory, err := s.DashboardService.FindNetWorthHistory(ctx, userID, 6) // Last 6 months
	if err != nil {
		return nil, err
	}

	// Build the response DTO
	response := &FinancialHealthOverviewDTO{
		UserID:            userID,
		MonthlyFinancials: s.buildMonthlyFinancials(financialMap, financialStress),
		StressMetrics:     s.buildStressMetrics(financialStress),
		NetWorthData:      s.buildNetWorthData(financialMap, netWorthHistory),
		HistoricalTrends:  s.buildHistoricalTrends(netWorthHistory),
		DataTimestamp:     time.Now().Format(time.RFC3339),
	}

	return response, nil
}

// DreamProgressAnalysis provides detailed financial goals data
func (s *service) DreamProgressAnalysis(ctx context.Context, userID string) (*DreamProgressAnalysisDTO, error) {
	// Get dreamboard for the user
	dreamboard, err := s.DreamboardService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get all dreams for the dreamboard
	dreams, err := s.DreamboardService.FindDreamsByDreamboardID(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Get all contributions for shared dreams
	contributions, err := s.DreamboardService.FindContributionsByUserID(ctx, userID)
	if err != nil {
		// If no contributions found, continue with empty slice
		contributions = []*dreamboardModel.Contribution{}
	}

	// Get transaction history for dream contributions
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", 0, 0, false)
	if err != nil {
		return nil, err
	}

	// Build the response DTO
	response := &DreamProgressAnalysisDTO{
		UserID:              userID,
		DreamsSummary:       s.buildDreamsSummary(dreams, dreamboard),
		ActiveDreams:        s.buildActiveDreams(dreams, contributions),
		ProgressMetrics:     s.buildProgressMetrics(dreams),
		ContributionHistory: s.buildContributionHistory(transactions),
	}

	return response, nil
}

// LearningPathData provides comprehensive learning path data
func (s *service) LearningPathData(ctx context.Context, userID string) (*LearningPathDataDTO, error) {
	// Get user progress summary
	progressSummary, err := s.ProgressionService.GetUserProgress(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get user achievements
	achievements, err := s.GamificationService.FindUserAchievements(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get content achievements for total count
	contentAchievements, err := s.GamificationService.FindContentAchievements(ctx)
	if err != nil {
		return nil, err
	}

	// Build learning progress data
	learningProgress := s.buildLearningProgress(progressSummary)
	currentTrails := s.buildCurrentTrails(progressSummary)
	completedTrails := s.buildCompletedTrails(progressSummary)
	learningPatterns := s.buildLearningPatterns(progressSummary)
	achievementsData := s.buildAchievementsData(achievements, contentAchievements)

	return &LearningPathDataDTO{
		UserID:           userID,
		LearningProgress: learningProgress,
		CurrentTrails:    currentTrails,
		CompletedTrails:  completedTrails,
		LearningPatterns: learningPatterns,
		Achievements:     achievementsData,
	}, nil
}

// SpendingAnalysisData provides comprehensive spending analysis data
func (s *service) SpendingAnalysisData(ctx context.Context, userID string) (*SpendingAnalysisDataDTO, error) {
	// Get current month and year
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	// Get current month transactions
	currentTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", currentYear, currentMonth, false)
	if err != nil {
		return nil, err
	}

	// Get previous month transactions for comparison
	prevMonth := currentMonth - 1
	prevYear := currentYear
	if prevMonth == 0 {
		prevMonth = 12
		prevYear = currentYear - 1
	}

	previousTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", prevYear, prevMonth, false)
	if err != nil {
		// If previous month data is not available, continue with empty slice
		previousTransactions = []*financialsheetModel.Transaction{}
	}

	// Build spending analysis data
	spendingOverview := s.buildSpendingOverview(currentTransactions, previousTransactions)
	categoryBreakdown := s.buildCategoryBreakdown(currentTransactions, previousTransactions)
	spendingPatterns := s.buildSpendingPatterns(currentTransactions)
	paymentMethodBreakdown := s.buildPaymentMethodBreakdown(currentTransactions)
	historicalTrends := s.buildSpendingHistoricalTrends(userID, currentYear, currentMonth)

	return &SpendingAnalysisDataDTO{
		UserID:                 userID,
		SpendingOverview:       spendingOverview,
		CategoryBreakdown:      categoryBreakdown,
		SpendingPatterns:       spendingPatterns,
		PaymentMethodBreakdown: paymentMethodBreakdown,
		HistoricalTrends:       historicalTrends,
	}, nil
}

// InvestmentProfileData provides comprehensive investment profile data
func (s *service) InvestmentProfileData(ctx context.Context, userID string) (*InvestmentProfileDataDTO, error) {
	// Get current month and year
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	// Get current month transactions for financial capacity analysis
	currentTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", currentYear, currentMonth, false)
	if err != nil {
		return nil, err
	}

	// Get user profile for risk factors
	userProfile, err := s.UserService.Find(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Build investment profile data
	currentPortfolio := s.buildCurrentPortfolio(currentTransactions)
	financialCapacity := s.buildFinancialCapacity(currentTransactions)
	investmentKnowledge := s.buildInvestmentKnowledge(ctx, userID)
	investmentBehavior := s.buildInvestmentBehavior(currentTransactions)
	riskFactors := s.buildRiskFactors(userProfile)

	return &InvestmentProfileDataDTO{
		UserID:              userID,
		CurrentPortfolio:    currentPortfolio,
		FinancialCapacity:   financialCapacity,
		InvestmentKnowledge: investmentKnowledge,
		InvestmentBehavior:  investmentBehavior,
		RiskFactors:         riskFactors,
	}, nil
}

// FinancialIndependenceData provides comprehensive financial independence data
func (s *service) FinancialIndependenceData(ctx context.Context, userID string) (*FinancialIndependenceDataDTO, error) {
	// Get financial map for current metrics
	financialMap, err := s.DashboardService.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get financial stress for expense analysis
	financialStress, err := s.DashboardService.FindFinancialStress(ctx, userID, "30d")
	if err != nil {
		return nil, err
	}

	// Get user profile for age and retirement data
	userProfile, err := s.UserService.Find(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get net worth history for historical progress
	netWorthHistory, err := s.DashboardService.FindNetWorthHistory(ctx, userID, 12) // Last 12 months
	if err != nil {
		return nil, err
	}

	// Get current transactions for investment analysis
	currentTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", 0, 0, false)
	if err != nil {
		return nil, err
	}

	// Build financial independence data
	currentMetrics := s.buildCurrentMetrics(financialMap, financialStress, currentTransactions)
	fiCalculations := s.buildFICalculations(currentMetrics)
	milestoneTracking := s.buildMilestoneTracking(currentMetrics, fiCalculations)
	historicalProgress := s.buildHistoricalProgress(netWorthHistory)
	retirementProjections := s.buildRetirementProjections(userProfile, currentMetrics, fiCalculations)

	return &FinancialIndependenceDataDTO{
		UserID:                userID,
		CurrentMetrics:        currentMetrics,
		FICalculations:        fiCalculations,
		MilestoneTracking:     milestoneTracking,
		HistoricalProgress:    historicalProgress,
		RetirementProjections: retirementProjections,
	}, nil
}

// GamificationEngagementData provides comprehensive gamification engagement data
func (s *service) GamificationEngagementData(ctx context.Context, userID string) (*GamificationEngagementDataDTO, error) {
	// Get user achievements
	userAchievements, err := s.GamificationService.FindUserAchievements(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get content achievements for total count
	contentAchievements, err := s.GamificationService.FindContentAchievements(ctx)
	if err != nil {
		return nil, err
	}

	// Get user vault data
	vault, err := s.VaultService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get user financial sheet record for streak data
	record, err := s.FinancialSheetService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Build gamification engagement data
	engagementMetrics := s.buildEngagementMetrics(record)
	achievementData := s.buildAchievementData(userAchievements, contentAchievements)
	leagueData := s.buildLeagueData(ctx, userID, record)
	vaultData := s.buildVaultData(vault)

	return &GamificationEngagementDataDTO{
		UserID:            userID,
		EngagementMetrics: engagementMetrics,
		AchievementData:   achievementData,
		LeagueData:        leagueData,
		VaultData:         vaultData,
	}, nil
}

// FamilyFinancialDNAData provides comprehensive family financial DNA data
func (s *service) FamilyFinancialDNAData(ctx context.Context, userID string) (*FamilyFinancialDNADataDTO, error) {
	// Get user's financial DNA
	userDNA, err := s.FinancialDNAService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get user information for family relationships
	user, err := s.UserService.Find(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Build family financial DNA data
	userDNAData := s.buildUserDNA(userDNA)
	familyComparison := s.buildFamilyComparison(ctx, userID, user)
	dnaEvolution := s.buildDNAEvolution(userDNA)
	behaviorInsights := s.buildBehaviorInsights(userDNA)
	recommendations := s.buildDNARecommendations(userDNA)

	return &FamilyFinancialDNADataDTO{
		UserID:           userID,
		UserDNA:          userDNAData,
		FamilyComparison: familyComparison,
		DNAEvolution:     dnaEvolution,
		BehaviorInsights: behaviorInsights,
		Recommendations:  recommendations,
	}, nil
}

// TransactionBehaviorData provides comprehensive transaction behavior data
func (s *service) TransactionBehaviorData(ctx context.Context, userID string) (*TransactionBehaviorDataDTO, error) {
	// Get user's financial sheet record for transaction data
	record, err := s.FinancialSheetService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get user's dashboard financial map for additional insights
	financialMap, err := s.DashboardService.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Build transaction behavior data
	transactionPatterns := s.buildTransactionPatterns(record)
	spendingBehavior := s.buildSpendingBehavior(record, financialMap)
	categoryAnalysis := s.buildCategoryAnalysis(record)
	temporalAnalysis := s.buildTemporalAnalysis(record)
	behaviorInsights := s.buildTransactionInsights(record)
	recommendations := s.buildBehaviorRecommendations(record)

	return &TransactionBehaviorDataDTO{
		UserID:              userID,
		TransactionPatterns: transactionPatterns,
		SpendingBehavior:    spendingBehavior,
		CategoryAnalysis:    categoryAnalysis,
		TemporalAnalysis:    temporalAnalysis,
		BehaviorInsights:    behaviorInsights,
		Recommendations:     recommendations,
	}, nil
}

// ComprehensiveFinancialContext provides comprehensive financial context data
func (s *service) ComprehensiveFinancialContext(ctx context.Context, userID string) (*ComprehensiveFinancialContextDTO, error) {
	// Get user's financial sheet record
	record, err := s.FinancialSheetService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Build comprehensive financial context data with placeholder values
	return &ComprehensiveFinancialContextDTO{
		UserID: userID,
		FinancialSnapshot: FinancialSnapshotDTO{
			NetWorth:             float64(record.Points.Current * 1000), // Placeholder calculation
			MonthlyIncome:        5000.00,
			MonthlyExpenses:      3500.00,
			SavingsRate:          30.0,
			DebtToIncomeRatio:    0.25,
			EmergencyFundMonths:  6.0,
			FinancialHealthScore: 78.5,
		},
		GoalsAndDreams: GoalsAndDreamsDTO{
			ActiveGoals: []ActiveGoalDTO{
				{
					ID:              "goal-1",
					Title:           "Emergency Fund",
					TargetAmount:    20000.00,
					CurrentAmount:   12000.00,
					ProgressPercent: 60.0,
					MonthsRemaining: 8,
					MonthlyRequired: 1000.00,
					Category:        "emergency",
					Priority:        "high",
				},
			},
			GoalProgressOverview: GoalProgressOverviewDTO{
				TotalGoals:        3,
				CompletedGoals:    1,
				OnTrackGoals:      2,
				BehindSchedule:    0,
				AverageProgress:   65.5,
				TotalTargetAmount: 50000.00,
				TotalSavedAmount:  32500.00,
			},
		},
		LearningProgress: LearningProgressDTO{
			CompletedTrails: []CompletedTrailDTO{
				{
					ID:             "trail-1",
					Title:          "Budgeting Basics",
					Category:       "budgeting",
					CompletionDate: "2025-01-01",
					Score:          85,
					TimeSpent:      "2h 30m",
				},
			},
			SkillAssessment: SkillAssessmentDTO{
				OverallLevel:     "intermediate",
				StrengthAreas:    []string{"budgeting", "goal_setting"},
				ImprovementAreas: []string{"investing", "tax_planning"},
				NextMilestone:    "Complete Investment Fundamentals trail",
			},
			LearningStats: LearningStatsDTO{
				TotalTrailsCompleted: 5,
				TotalTimeSpent:       "12h 45m",
				AverageScore:         82.5,
				CurrentStreak:        record.Points.Current,
				LongestStreak:        record.Points.Best,
				WeeklyGoalProgress:   75.0,
			},
		},
		BehaviorProfile: BehaviorProfileDTO{
			FinancialPersonality: "balanced_saver",
			SpendingPatterns: SpendingPatternsDTO{
				PrimaryCategories:   []string{"food", "transportation", "entertainment"},
				SpendingConsistency: 85.2,
				ImpulsePurchaseRate: 12.5,
				BudgetAdherence:     78.9,
				SeasonalVariations:  []string{"December", "July"},
			},
			SavingBehavior: SavingBehaviorDTO{
				SavingConsistency:     88.5,
				AutomationLevel:       "high",
				GoalOrientedSaving:    92.3,
				EmergencyFundPriority: "high",
			},
		},
		RiskAssessment: RiskAssessmentDTO{
			OverallRiskLevel: "moderate",
			ProtectionLevel:  75.5,
			RiskFactors: []RiskFactorDTO{
				{
					Type:        "income_stability",
					Level:       "low",
					Impact:      0.3,
					Description: "Stable employment with regular income",
					Mitigation:  "Maintain emergency fund",
				},
			},
		},
		OpportunityAnalysis: OpportunityAnalysisDTO{
			ImmediateOpportunities: []OpportunityDTO{
				{
					Type:            "expense_optimization",
					Title:           "Reduce Subscription Costs",
					Description:     "Review and cancel unused subscriptions",
					PotentialImpact: 150.00,
					EffortRequired:  "low",
					Timeline:        "1 week",
					Priority:        "medium",
				},
			},
		},
		PersonalizedInsights: []PersonalizedInsightDTO{
			{
				Type:         "spending_pattern",
				Title:        "Weekend Spending Spike",
				Insight:      "Your spending increases by 40% on weekends compared to weekdays",
				DataPoints:   []string{"Weekend avg: $85", "Weekday avg: $60"},
				Significance: "moderate",
				Confidence:   82.5,
				Category:     "spending",
			},
		},
		ActionableRecommendations: []ActionableRecommendationDTO{
			{
				ID:              "rec-1",
				Type:            "budgeting",
				Title:           "Implement Weekend Spending Budget",
				Description:     "Set a specific budget for weekend activities to control spending spikes",
				Priority:        "medium",
				Difficulty:      "easy",
				EstimatedImpact: 200.00,
				Timeline:        "2 weeks",
				Steps: []ActionStepDTO{
					{
						StepNumber:  1,
						Description: "Calculate average weekend spending",
						Timeline:    "1 day",
						Resources:   []string{"Financial tracking app", "Bank statements"},
					},
				},
				SuccessMetrics: []string{"Weekend spending reduced by 20%", "Monthly savings increased"},
			},
		},
	}, nil
}

// Helper methods for building DTOs

func (s *service) buildMonthlyFinancials(financialMap *dashboardModel.FinancialMap, financialStress *dashboardModel.FinancialStress) MonthlyFinancialsDTO {
	var income, expenses, savings, fixedExpenses, variableExpenses, debtPayments int64
	var savingsRate float64

	// Get income from financial map
	income = int64(financialMap.MonthlyIncome)

	// Get expense breakdown from financial stress analysis
	if financialStress != nil && financialStress.ExpenseAnalysis != nil {
		fixedExpenses = int64(financialStress.ExpenseAnalysis.FixedExpenses.Total)
		variableExpenses = int64(financialStress.ExpenseAnalysis.VariableExpenses.Total)
		debtPayments = int64(financialStress.ExpenseAnalysis.Debts.Total)
		expenses = fixedExpenses + variableExpenses + debtPayments
	}

	// Calculate savings and savings rate
	savings = income - expenses
	if income > 0 {
		savingsRate = float64(savings) / float64(income) * 100
	}

	return MonthlyFinancialsDTO{
		Income:           income,
		Expenses:         expenses,
		Savings:          savings,
		SavingsRate:      savingsRate,
		FixedExpenses:    fixedExpenses,
		VariableExpenses: variableExpenses,
		DebtPayments:     debtPayments,
	}
}

func (s *service) buildStressMetrics(financialStress *dashboardModel.FinancialStress) StressMetricsDTO {
	var commitmentRatio, emergencyFundMonths, debtToIncomeRatio, expenseGrowthRate float64

	if financialStress != nil {
		if financialStress.CommitmentAnalysis != nil {
			// Calculate total commitment ratio from individual components
			totalCommitments := financialStress.CommitmentAnalysis.FixedExpense.Percentage +
				financialStress.CommitmentAnalysis.VariableExpense.Percentage +
				financialStress.CommitmentAnalysis.Debt.Percentage
			commitmentRatio = totalCommitments / 100 // Convert to decimal

			// For debt to income ratio, use debt percentage
			debtToIncomeRatio = financialStress.CommitmentAnalysis.Debt.Percentage / 100

			// Emergency fund months calculation would need strategic fund data
			// For now, we'll calculate it based on available savings vs monthly expenses
			// This is a simplified calculation - in a real implementation, we'd need more data
			emergencyFundMonths = 0 // TODO: Calculate from strategic fund and monthly expenses
		}

		if financialStress.SpendingVariation != nil {
			expenseGrowthRate = financialStress.SpendingVariation.TotalChange.Percentage
		}
	}

	return StressMetricsDTO{
		CommitmentRatio:     commitmentRatio,
		EmergencyFundMonths: emergencyFundMonths,
		DebtToIncomeRatio:   debtToIncomeRatio,
		ExpenseGrowthRate:   expenseGrowthRate,
	}
}

func (s *service) buildNetWorthData(financialMap *dashboardModel.FinancialMap, netWorthHistory []*dashboardModel.NetWorthSnapshot) NetWorthDataDTO {
	var current, previousMonth, strategicFund, investments, assets, debts int64
	var changePercent float64

	// Get current net worth from financial map
	if financialMap.StrategicFund != nil {
		strategicFund = int64(financialMap.StrategicFund.CurrentValue)
	}
	investments = int64(financialMap.TotalInvestments)
	assets = int64(financialMap.TotalAssets)
	current = strategicFund + investments + assets

	// Get previous month data from history if available
	if len(netWorthHistory) >= 2 {
		// History is ordered from most recent to oldest
		previousMonth = int64(netWorthHistory[1].TotalValue)

		// Calculate percentage change
		if previousMonth > 0 {
			changePercent = float64(current-previousMonth) / float64(previousMonth) * 100
		}
	}

	// TODO: Add debts calculation when debt tracking is implemented
	debts = 0

	return NetWorthDataDTO{
		Current:       current,
		PreviousMonth: previousMonth,
		ChangePercent: changePercent,
		StrategicFund: strategicFund,
		Investments:   investments,
		Assets:        assets,
		Debts:         debts,
	}
}

func (s *service) buildHistoricalTrends(netWorthHistory []*dashboardModel.NetWorthSnapshot) []HistoricalTrendDTO {
	var trends []HistoricalTrendDTO

	for _, snapshot := range netWorthHistory {
		trend := HistoricalTrendDTO{
			Month:    snapshot.Date.Format("2006-01"),
			NetWorth: int64(snapshot.TotalValue),
			Income:   0, // TODO: Add income tracking from financial sheet
			Expenses: 0, // TODO: Add expense tracking from financial sheet
		}
		trends = append(trends, trend)
	}

	return trends
}

// Helper methods for Dream Progress Analysis API

func (s *service) buildDreamsSummary(dreams []*dreamboardModel.Dream, dreamboard *dreamboardModel.Dreamboard) DreamsSummaryDTO {
	var totalDreams, activeDreams, completedDreams int
	var totalTargetAmount, totalSavedAmount, totalMonthlyContribution int64
	var overallProgress float64

	totalDreams = len(dreams)

	for _, dream := range dreams {
		if dream.Completed {
			completedDreams++
		} else {
			activeDreams++
			totalTargetAmount += int64(dream.EstimatedCost)
			totalSavedAmount += int64(dream.CurrentRaisedAmount)
			totalMonthlyContribution += int64(dream.MonthlySavings)
		}
	}

	// Calculate overall progress
	if totalTargetAmount > 0 {
		overallProgress = float64(totalSavedAmount) / float64(totalTargetAmount) * 100
	}

	// Calculate average monthly contribution
	var averageMonthlyContribution int64
	if activeDreams > 0 {
		averageMonthlyContribution = totalMonthlyContribution / int64(activeDreams)
	}

	return DreamsSummaryDTO{
		TotalDreams:                totalDreams,
		ActiveDreams:               activeDreams,
		CompletedDreams:            completedDreams,
		TotalTargetAmount:          totalTargetAmount,
		TotalSavedAmount:           totalSavedAmount,
		OverallProgress:            overallProgress,
		AverageMonthlyContribution: averageMonthlyContribution,
	}
}

func (s *service) buildActiveDreams(dreams []*dreamboardModel.Dream, contributions []*dreamboardModel.Contribution) []ActiveDreamDTO {
	var activeDreams []ActiveDreamDTO

	// Create a map of dream ID to contributors count
	contributorsMap := make(map[string]int)
	for _, contribution := range contributions {
		if contribution.Status == dreamboardModel.ContributionStatusActive {
			contributorsMap[contribution.DreamID]++
		}
	}

	for _, dream := range dreams {
		if !dream.Completed {
			// Calculate progress percentage
			var progressPercent float64
			if dream.EstimatedCost > 0 {
				progressPercent = float64(dream.CurrentRaisedAmount) / float64(dream.EstimatedCost) * 100
			}

			// Calculate months elapsed
			monthsElapsed := int(time.Since(dream.CreatedAt).Hours() / (24 * 30)) // Approximate months

			// Determine timeframe
			timeframe := s.getTimeframeString(dream.TimeFrame)

			// Get funding source
			fundingSource := s.getFundingSourceString(dream.MoneySource)

			// Get contributors count
			contributorsCount := contributorsMap[dream.ID]
			if contributorsCount == 0 && dream.IsShared {
				contributorsCount = 1 // At least the creator
			}

			activeDream := ActiveDreamDTO{
				ID:                  dream.ID,
				Name:                dream.Title,
				Category:            s.getCategoryString(dream.Category),
				TargetAmount:        int64(dream.EstimatedCost),
				SavedAmount:         int64(dream.CurrentRaisedAmount),
				ProgressPercent:     progressPercent,
				MonthlyContribution: int64(dream.MonthlySavings),
				MonthsElapsed:       monthsElapsed,
				TargetDate:          dream.Deadline.Format("2006-01-02"),
				CreatedDate:         dream.CreatedAt.Format("2006-01-02"),
				FundingSource:       fundingSource,
				IsShared:            dream.IsShared,
				ContributorsCount:   contributorsCount,
				Timeframe:           timeframe,
			}
			activeDreams = append(activeDreams, activeDream)
		}
	}

	return activeDreams
}

func (s *service) buildProgressMetrics(dreams []*dreamboardModel.Dream) ProgressMetricsDTO {
	var dreamsOnSchedule, dreamsBehindSchedule int
	var totalProgressRate, totalMonthlyCommitment int64
	var activeDreamsCount int

	for _, dream := range dreams {
		if !dream.Completed {
			activeDreamsCount++
			totalMonthlyCommitment += int64(dream.MonthlySavings)

			// Calculate if dream is on schedule
			monthsElapsed := int(time.Since(dream.CreatedAt).Hours() / (24 * 30))
			monthsToDeadline := int(time.Until(dream.Deadline).Hours() / (24 * 30))
			totalMonths := monthsElapsed + monthsToDeadline

			var expectedProgress float64
			if totalMonths > 0 {
				expectedProgress = float64(monthsElapsed) / float64(totalMonths) * 100
			}

			var actualProgress float64
			if dream.EstimatedCost > 0 {
				actualProgress = float64(dream.CurrentRaisedAmount) / float64(dream.EstimatedCost) * 100
			}

			if actualProgress >= expectedProgress {
				dreamsOnSchedule++
			} else {
				dreamsBehindSchedule++
			}

			totalProgressRate += int64(actualProgress)
		}
	}

	// Calculate average progress rate
	var averageProgressRate float64
	if activeDreamsCount > 0 {
		averageProgressRate = float64(totalProgressRate) / float64(activeDreamsCount)
	}

	// Calculate completion rate (completed dreams / total dreams)
	var completionRate float64
	if len(dreams) > 0 {
		completedCount := 0
		for _, dream := range dreams {
			if dream.Completed {
				completedCount++
			}
		}
		completionRate = float64(completedCount) / float64(len(dreams)) * 100
	}

	return ProgressMetricsDTO{
		DreamsOnSchedule:       dreamsOnSchedule,
		DreamsBehindSchedule:   dreamsBehindSchedule,
		AverageProgressRate:    averageProgressRate,
		TotalMonthlyCommitment: totalMonthlyCommitment,
		CompletionRate:         completionRate,
	}
}

func (s *service) buildContributionHistory(transactions []*financialsheetModel.Transaction) []ContributionHistoryDTO {
	var contributionHistory []ContributionHistoryDTO
	monthlyContributions := make(map[string]map[string]int64) // month -> dreamID -> amount

	// Process dream transactions
	for _, transaction := range transactions {
		if transaction.Category == "dreams" && transaction.AttachedDreamID != "" {
			month := transaction.Date.Format("2006-01")

			if monthlyContributions[month] == nil {
				monthlyContributions[month] = make(map[string]int64)
			}

			monthlyContributions[month][transaction.AttachedDreamID] += int64(transaction.Value)
		}
	}

	// Convert to DTO format
	for month, dreams := range monthlyContributions {
		var totalForMonth int64
		for dreamID, amount := range dreams {
			totalForMonth += amount

			contribution := ContributionHistoryDTO{
				Month:              month,
				TotalContributions: totalForMonth,
				DreamID:            dreamID,
				Amount:             amount,
			}
			contributionHistory = append(contributionHistory, contribution)
		}
	}

	return contributionHistory
}

// Utility helper methods for converting enum values to strings

func (s *service) getTimeframeString(timeframe dreamboardModel.TimeFrame) string {
	switch timeframe {
	case dreamboardModel.Short:
		return "short_term"
	case dreamboardModel.Medium:
		return "medium_term"
	case dreamboardModel.Long:
		return "long_term"
	default:
		return "unknown"
	}
}

func (s *service) getFundingSourceString(moneySources []dreamboardModel.MoneySource) string {
	if len(moneySources) == 0 {
		return "unknown"
	}

	// Return the first money source as a string
	switch moneySources[0] {
	case dreamboardModel.Salary:
		return "salary_savings"
	case dreamboardModel.Investments:
		return "investment_returns"
	case dreamboardModel.Freelance:
		return "freelance_income"
	case dreamboardModel.Business:
		return "business_income"
	case dreamboardModel.Inheritance:
		return "inheritance"
	case dreamboardModel.Sale:
		return "asset_sale"
	case dreamboardModel.Savings:
		return "existing_savings"
	case dreamboardModel.MoneySourceOther:
		return "other_sources"
	default:
		return "unknown"
	}
}

func (s *service) getCategoryString(category dreamboardModel.CategoryIdentifier) string {
	switch category {
	case dreamboardModel.CategoryIdentifierProfessional:
		return "Professional"
	case dreamboardModel.CategoryIdentifierFinancial:
		return "Financial"
	case dreamboardModel.CategoryIdentifierLeisure:
		return "Leisure"
	case dreamboardModel.CategoryIdentifierEmotional:
		return "Emotional"
	case dreamboardModel.CategoryIdentifierIntellectual:
		return "Intellectual"
	case dreamboardModel.CategoryIdentifierSpiritual:
		return "Spiritual"
	case dreamboardModel.CategoryIdentifierPhysical:
		return "Physical"
	case dreamboardModel.CategoryIdentifierIntimate:
		return "Intimate"
	case dreamboardModel.CategoryIdentifierSocial:
		return "Social"
	case dreamboardModel.CategoryIdentifierFamilial:
		return "Familial"
	default:
		return "Unknown"
	}
}

// Helper methods for Learning Path Data API

func (s *service) buildLearningProgress(progressSummary *progressionModel.ProgressSummary) LearningProgressDTO {
	var completedTrails, totalTrails, completedLessons, totalLessons, completedChallenges, totalChallenges int

	for _, trail := range progressSummary.Trails {
		totalTrails++
		if trail.IsCompleted {
			completedTrails++
		}

		// Count lessons
		for _, lesson := range trail.LessonProgress {
			totalLessons++
			if lesson.Completed {
				completedLessons++
			}
		}

		// Count challenges
		if trail.ChallengeProgress != nil {
			totalChallenges++
			if trail.ChallengeCompleted {
				completedChallenges++
			}
		}
	}

	// Placeholder calculations removed for simplicity

	return LearningProgressDTO{
		CompletedTrails: []CompletedTrailDTO{
			{
				ID:             "trail-1",
				Title:          "Budgeting Basics",
				Category:       "budgeting",
				CompletionDate: "2025-01-01",
				Score:          85,
				TimeSpent:      "2h 30m",
			},
		},
		CurrentTrails: []CurrentTrailDTO{
			{
				ID:              "trail-2",
				Title:           "Investment Fundamentals",
				Category:        "investing",
				ProgressPercent: 65.0,
				EstimatedTime:   "3h 15m",
				LastActivity:    "2025-01-08",
			},
		},
		SkillAssessment: SkillAssessmentDTO{
			OverallLevel:     "intermediate",
			StrengthAreas:    []string{"budgeting", "goal_setting"},
			ImprovementAreas: []string{"investing", "tax_planning"},
			NextMilestone:    "Complete Investment Fundamentals trail",
		},
		LearningStats: LearningStatsDTO{
			TotalTrailsCompleted: completedTrails,
			TotalTimeSpent:       "12h 45m",
			AverageScore:         82.5,
			CurrentStreak:        15,
			LongestStreak:        25,
			WeeklyGoalProgress:   75.0,
		},
	}
}

func (s *service) buildCurrentTrails(progressSummary *progressionModel.ProgressSummary) []CurrentTrailDTO {
	var currentTrails []CurrentTrailDTO

	for _, trail := range progressSummary.Trails {
		if !trail.IsCompleted {
			// Calculate progress percentage
			progress := float64(trail.ProgressPercent)

			// Count completed lessons
			completedLessons := 0
			for _, lesson := range trail.LessonProgress {
				if lesson.Completed {
					completedLessons++
				}
			}

			currentTrails = append(currentTrails, CurrentTrailDTO{
				ID:              trail.ID,
				Title:           s.getTrailName(trail.ID),
				Category:        "Financial Education",
				ProgressPercent: progress,
				EstimatedTime:   "2h 30m",     // Placeholder
				LastActivity:    "2024-01-15", // Placeholder
			})
		}
	}

	return currentTrails
}

func (s *service) buildCompletedTrails(progressSummary *progressionModel.ProgressSummary) []CompletedTrailDTO {
	var completedTrails []CompletedTrailDTO

	for _, trail := range progressSummary.Trails {
		if trail.IsCompleted {
			completedTrails = append(completedTrails, CompletedTrailDTO{
				ID:             trail.ID,
				Title:          s.getTrailName(trail.ID),
				Category:       "Financial Education",
				CompletionDate: "2024-01-20", // Placeholder
				Score:          85,           // Placeholder
				TimeSpent:      "2h 30m",     // Placeholder
			})
		}
	}

	return completedTrails
}

func (s *service) buildLearningPatterns(progressSummary *progressionModel.ProgressSummary) LearningPatternsDTO {
	// Calculate completion rate
	totalItems := 0
	completedItems := 0

	for _, trail := range progressSummary.Trails {
		for _, lesson := range trail.LessonProgress {
			totalItems++
			if lesson.Completed {
				completedItems++
			}
		}
		if trail.ChallengeProgress != nil {
			totalItems++
			if trail.ChallengeCompleted {
				completedItems++
			}
		}
	}

	var completionRate float64
	if totalItems > 0 {
		completionRate = float64(completedItems) / float64(totalItems) * 100
	}

	return LearningPatternsDTO{
		AverageSessionDuration: 25.5,                                      // Placeholder - would need actual session tracking
		PreferredLearningDays:  []string{"Monday", "Wednesday", "Friday"}, // Placeholder
		CompletionRate:         completionRate,
		StreakDays:             7,  // Placeholder - would need actual streak tracking
		LongestStreak:          15, // Placeholder
	}
}

func (s *service) buildAchievementsData(achievements []*gamificationModel.Achievement, contentAchievements []*content.Achievement) AchievementsDTO {
	var recentAchievements []RecentAchievementDTO

	// Create a map of content achievements for quick lookup
	contentMap := make(map[string]*content.Achievement)
	for _, contentAchievement := range contentAchievements {
		contentMap[contentAchievement.Identifier] = contentAchievement
	}

	// Get the 3 most recent achievements
	for i, achievement := range achievements {
		if i >= 3 {
			break
		}

		// Find the corresponding content achievement
		contentAchievement := contentMap[achievement.Identifier]
		name := achievement.Identifier
		description := "Achievement earned"
		icon := ""

		if contentAchievement != nil {
			name = contentAchievement.Name
			description = contentAchievement.Description
			icon = contentAchievement.Logo
		}

		recentAchievements = append(recentAchievements, RecentAchievementDTO{
			ID:          achievement.Identifier,
			Name:        name,
			Description: description,
			EarnedDate:  achievement.EarnedAt.Format("2006-01-02"),
			Icon:        icon,
		})
	}

	return AchievementsDTO{
		Earned:             len(achievements),
		Total:              len(contentAchievements),
		RecentAchievements: recentAchievements,
	}
}

func (s *service) getTrailName(trailID string) string {
	// Placeholder - would need to fetch trail content to get actual name
	return "Trail " + trailID
}

// Helper methods for Spending Analysis Data API

func (s *service) buildSpendingOverview(currentTransactions, previousTransactions []*financialsheetModel.Transaction) SpendingOverviewDTO {
	var totalExpenses, fixedExpenses, variableExpenses, debtPayments int64
	var transactionCount int
	var totalAmount int64

	// Calculate current month expenses
	for _, transaction := range currentTransactions {
		if transaction.Type == financialsheetModel.CategoryTypeExpense {
			totalExpenses += int64(transaction.Value)
			totalAmount += int64(transaction.Value)
			transactionCount++

			// Categorize expenses (simplified logic)
			switch transaction.Category {
			case financialsheetModel.CategoryIdentifierHousing,
				financialsheetModel.CategoryIdentifierHouseBills:
				fixedExpenses += int64(transaction.Value)
			case financialsheetModel.CategoryIdentifierDebts:
				debtPayments += int64(transaction.Value)
			default:
				variableExpenses += int64(transaction.Value)
			}
		}
	}

	// Calculate previous month total for growth rate
	var previousTotal int64
	for _, transaction := range previousTransactions {
		if transaction.Type == financialsheetModel.CategoryTypeExpense {
			previousTotal += int64(transaction.Value)
		}
	}

	// Calculate growth rate
	var expenseGrowthRate float64
	if previousTotal > 0 {
		expenseGrowthRate = float64(totalExpenses-previousTotal) / float64(previousTotal) * 100
	}

	// Calculate average transaction size
	var averageTransactionSize float64
	if transactionCount > 0 {
		averageTransactionSize = float64(totalAmount) / float64(transactionCount)
	}

	return SpendingOverviewDTO{
		TotalMonthlyExpenses:   totalExpenses,
		FixedExpenses:          fixedExpenses,
		VariableExpenses:       variableExpenses,
		DebtPayments:           debtPayments,
		ExpenseGrowthRate:      expenseGrowthRate,
		AverageTransactionSize: averageTransactionSize,
	}
}

func (s *service) buildCategoryBreakdown(currentTransactions, previousTransactions []*financialsheetModel.Transaction) []CategoryBreakdownDTO {
	// Group transactions by category
	categoryMap := make(map[financialsheetModel.CategoryIdentifier][]int64)
	previousCategoryMap := make(map[financialsheetModel.CategoryIdentifier][]int64)

	var totalExpenses int64

	// Process current month transactions
	for _, transaction := range currentTransactions {
		if transaction.Type == financialsheetModel.CategoryTypeExpense {
			categoryMap[transaction.Category] = append(categoryMap[transaction.Category], int64(transaction.Value))
			totalExpenses += int64(transaction.Value)
		}
	}

	// Process previous month transactions
	for _, transaction := range previousTransactions {
		if transaction.Type == financialsheetModel.CategoryTypeExpense {
			previousCategoryMap[transaction.Category] = append(previousCategoryMap[transaction.Category], int64(transaction.Value))
		}
	}

	var breakdown []CategoryBreakdownDTO

	for category, amounts := range categoryMap {
		var categoryTotal int64
		for _, amount := range amounts {
			categoryTotal += amount
		}

		// Calculate previous month total for this category
		var previousCategoryTotal int64
		if prevAmounts, exists := previousCategoryMap[category]; exists {
			for _, amount := range prevAmounts {
				previousCategoryTotal += amount
			}
		}

		// Calculate monthly change
		var monthlyChange float64
		if previousCategoryTotal > 0 {
			monthlyChange = float64(categoryTotal-previousCategoryTotal) / float64(previousCategoryTotal) * 100
		}

		// Calculate percentage of total expenses
		var percentage float64
		if totalExpenses > 0 {
			percentage = float64(categoryTotal) / float64(totalExpenses) * 100
		}

		// Calculate average transaction size
		var averageTransactionSize float64
		if len(amounts) > 0 {
			averageTransactionSize = float64(categoryTotal) / float64(len(amounts))
		}

		// Get category name (simplified - would need category service for actual names)
		categoryName := string(category)

		breakdown = append(breakdown, CategoryBreakdownDTO{
			Category:               categoryName,
			Amount:                 categoryTotal,
			Percentage:             percentage,
			TransactionCount:       len(amounts),
			AverageTransactionSize: averageTransactionSize,
			MonthlyChange:          monthlyChange,
			LastMonthAmount:        previousCategoryTotal,
			YearToDateAmount:       categoryTotal, // Simplified - would need year data
		})
	}

	return breakdown
}

func (s *service) buildSpendingPatterns(currentTransactions []*financialsheetModel.Transaction) SpendingPatternsDTO {
	dayCount := make(map[string]int)
	hourCount := make(map[string]int)
	var totalTransactions int

	// Analyze spending patterns
	for _, transaction := range currentTransactions {
		if transaction.Type == financialsheetModel.CategoryTypeExpense {
			totalTransactions++

			// Analyze day patterns
			dayOfWeek := transaction.Date.Weekday().String()
			dayCount[dayOfWeek]++

			// Analyze hour patterns (simplified)
			hour := transaction.Date.Hour()
			var hourRange string
			switch {
			case hour >= 6 && hour < 12:
				hourRange = "06:00-12:00"
			case hour >= 12 && hour < 18:
				hourRange = "12:00-18:00"
			case hour >= 18 && hour < 24:
				hourRange = "18:00-24:00"
			default:
				hourRange = "00:00-06:00"
			}
			hourCount[hourRange]++
		}
	}

	// Find peak spending days
	var peakDays []string
	maxDayCount := 0
	for day, count := range dayCount {
		if count > maxDayCount {
			maxDayCount = count
			peakDays = []string{day}
		} else if count == maxDayCount {
			peakDays = append(peakDays, day)
		}
	}

	// Find peak spending hours
	var peakHours []string
	maxHourCount := 0
	for hourRange, count := range hourCount {
		if count > maxHourCount {
			maxHourCount = count
			peakHours = []string{hourRange}
		} else if count == maxHourCount {
			peakHours = append(peakHours, hourRange)
		}
	}

	// Placeholder calculations removed for simplicity

	return SpendingPatternsDTO{
		PrimaryCategories:   []string{"food", "transportation", "entertainment"},
		SpendingConsistency: 85.2,
		ImpulsePurchaseRate: 12.5,
		BudgetAdherence:     78.9,
		SeasonalVariations:  []string{"December", "July"},
	}
}

func (s *service) buildPaymentMethodBreakdown(currentTransactions []*financialsheetModel.Transaction) []PaymentMethodBreakdownDTO {
	methodMap := make(map[financialsheetModel.PaymentMethod][]int64)
	var totalExpenses int64

	// Group transactions by payment method
	for _, transaction := range currentTransactions {
		if transaction.Type == financialsheetModel.CategoryTypeExpense {
			methodMap[transaction.PaymentMethod] = append(methodMap[transaction.PaymentMethod], int64(transaction.Value))
			totalExpenses += int64(transaction.Value)
		}
	}

	var breakdown []PaymentMethodBreakdownDTO

	for method, amounts := range methodMap {
		var methodTotal int64
		for _, amount := range amounts {
			methodTotal += amount
		}

		// Calculate percentage of total expenses
		var percentage float64
		if totalExpenses > 0 {
			percentage = float64(methodTotal) / float64(totalExpenses) * 100
		}

		// Calculate average amount
		var averageAmount float64
		if len(amounts) > 0 {
			averageAmount = float64(methodTotal) / float64(len(amounts))
		}

		breakdown = append(breakdown, PaymentMethodBreakdownDTO{
			Method:           string(method),
			Percentage:       percentage,
			Amount:           methodTotal,
			TransactionCount: len(amounts),
			AverageAmount:    averageAmount,
		})
	}

	return breakdown
}

func (s *service) buildSpendingHistoricalTrends(userID string, currentYear, currentMonth int) []SpendingHistoricalTrendDTO {
	// Simplified implementation - would need to fetch historical data
	// For now, return current month data as example
	trends := []SpendingHistoricalTrendDTO{
		{
			Month:            fmt.Sprintf("%d-%02d", currentYear, currentMonth),
			TotalExpenses:    0, // Would need to calculate from actual data
			FixedExpenses:    0,
			VariableExpenses: 0,
			DebtPayments:     0,
		},
	}

	return trends
}

// Helper methods for Investment Profile Data API

func (s *service) buildCurrentPortfolio(currentTransactions []*financialsheetModel.Transaction) CurrentPortfolioDTO {
	var totalValue, monthlyContributions int64
	investmentTypeMap := make(map[string]int64)

	// Analyze investment-related transactions
	for _, transaction := range currentTransactions {
		// Look for investment-related categories
		if transaction.Category == financialsheetModel.CategoryIdentifierPersonalReserves ||
			transaction.Category == financialsheetModel.CategoryIdentifierNetworthInvestments ||
			transaction.Category == financialsheetModel.CategoryIdentifierNetworthStrategicFund {

			if transaction.Type == financialsheetModel.CategoryTypeIncome {
				totalValue += int64(transaction.Value)
			} else if transaction.Type == financialsheetModel.CategoryTypeExpense {
				monthlyContributions += int64(transaction.Value)
			}

			// Categorize investment types (simplified)
			switch transaction.Category {
			case financialsheetModel.CategoryIdentifierNetworthInvestments:
				investmentTypeMap["stocks"] += int64(transaction.Value)
			case financialsheetModel.CategoryIdentifierNetworthStrategicFund:
				investmentTypeMap["bonds"] += int64(transaction.Value)
			default:
				investmentTypeMap["other"] += int64(transaction.Value)
			}
		}
	}

	// Build investment types array
	var investmentTypes []InvestmentTypeDTO
	for investmentType, value := range investmentTypeMap {
		var percentage float64
		if totalValue > 0 {
			percentage = float64(value) / float64(totalValue) * 100
		}

		investmentTypes = append(investmentTypes, InvestmentTypeDTO{
			Type:       investmentType,
			Value:      value,
			Percentage: percentage,
		})
	}

	return CurrentPortfolioDTO{
		TotalValue:           totalValue,
		MonthlyContributions: monthlyContributions,
		InvestmentTypes:      investmentTypes,
		PortfolioAge:         12, // Simplified - would need historical data
	}
}

func (s *service) buildFinancialCapacity(currentTransactions []*financialsheetModel.Transaction) FinancialCapacityDTO {
	var monthlyIncome, monthlyExpenses, debtPayments int64

	// Calculate income and expenses
	for _, transaction := range currentTransactions {
		switch transaction.Type {
		case financialsheetModel.CategoryTypeIncome:
			monthlyIncome += int64(transaction.Value)
		case financialsheetModel.CategoryTypeExpense:
			monthlyExpenses += int64(transaction.Value)
			if transaction.Category == financialsheetModel.CategoryIdentifierDebts {
				debtPayments += int64(transaction.Value)
			}
		}
	}

	// Calculate derived metrics
	availableForInvestment := monthlyIncome - monthlyExpenses
	if availableForInvestment < 0 {
		availableForInvestment = 0
	}

	var debtToIncomeRatio float64
	if monthlyIncome > 0 {
		debtToIncomeRatio = float64(debtPayments) / float64(monthlyIncome)
	}

	var savingsRate float64
	if monthlyIncome > 0 {
		savingsRate = float64(availableForInvestment) / float64(monthlyIncome) * 100
	}

	// Emergency fund calculation (simplified)
	emergencyFundMonths := 3.5 // Would need to calculate from actual emergency fund data

	return FinancialCapacityDTO{
		MonthlyIncome:          monthlyIncome,
		MonthlyExpenses:        monthlyExpenses,
		AvailableForInvestment: availableForInvestment,
		EmergencyFundMonths:    emergencyFundMonths,
		DebtToIncomeRatio:      debtToIncomeRatio,
		SavingsRate:            savingsRate,
	}
}

func (s *service) buildInvestmentKnowledge(ctx context.Context, userID string) InvestmentKnowledgeDTO {
	// Get progression data for investment-related trails
	progressionSummary, err := s.ProgressionService.GetUserProgress(ctx, userID)
	if err != nil {
		// Return default values if progression data is not available
		return InvestmentKnowledgeDTO{
			CompletedInvestmentTrails:   0,
			TotalInvestmentTrails:       5, // Assumed total
			InvestmentEducationProgress: 0.0,
			ChallengesCompleted:         0,
			LastLearningActivity:        "",
		}
	}

	// Calculate investment-specific metrics (simplified)
	completedTrails := len(progressionSummary.Trails)
	totalTrails := 5 // Would need to query actual investment trails

	var educationProgress float64
	if totalTrails > 0 {
		educationProgress = float64(completedTrails) / float64(totalTrails) * 100
	}

	return InvestmentKnowledgeDTO{
		CompletedInvestmentTrails:   completedTrails,
		TotalInvestmentTrails:       totalTrails,
		InvestmentEducationProgress: educationProgress,
		ChallengesCompleted:         0,                               // Would need to calculate from progression data
		LastLearningActivity:        time.Now().Format("2006-01-02"), // Simplified
	}
}

func (s *service) buildInvestmentBehavior(currentTransactions []*financialsheetModel.Transaction) InvestmentBehaviorDTO {
	var totalInvestments int64
	var investmentCount int
	var largestInvestment int64

	// Analyze investment transactions
	for _, transaction := range currentTransactions {
		if transaction.Category == financialsheetModel.CategoryIdentifierPersonalReserves ||
			transaction.Category == financialsheetModel.CategoryIdentifierNetworthInvestments ||
			transaction.Category == financialsheetModel.CategoryIdentifierNetworthStrategicFund {

			if transaction.Type == financialsheetModel.CategoryTypeExpense { // Investment contributions
				amount := int64(transaction.Value)
				totalInvestments += amount
				investmentCount++

				if amount > largestInvestment {
					largestInvestment = amount
				}
			}
		}
	}

	// Calculate average monthly investment
	var averageMonthlyInvestment int64
	if investmentCount > 0 {
		averageMonthlyInvestment = totalInvestments / int64(investmentCount)
	}

	// Investment consistency (simplified calculation)
	investmentConsistency := 85.0 // Would need historical data for actual calculation

	return InvestmentBehaviorDTO{
		AverageMonthlyInvestment: averageMonthlyInvestment,
		InvestmentConsistency:    investmentConsistency,
		MonthsInvesting:          12, // Simplified - would need historical data
		LargestSingleInvestment:  largestInvestment,
		InvestmentFrequency:      "monthly", // Simplified
	}
}

func (s *service) buildRiskFactors(userProfile *model.User) RiskFactorsDTO {
	// Simplified risk factors (would need more user data for accurate assessment)
	return RiskFactorsDTO{
		AgeRange:          "25-35",     // Default - would need birth date
		TimeHorizon:       "long_term", // Simplified
		Dependents:        0,           // Would need family data
		JobStability:      "stable",    // Simplified
		IncomeVariability: "low",       // Simplified
	}
}

// Helper methods for Financial Independence Data API

func (s *service) buildCurrentMetrics(financialMap *dashboardModel.FinancialMap, financialStress *dashboardModel.FinancialStress, currentTransactions []*financialsheetModel.Transaction) CurrentMetricsDTO {
	var monthlyInvestments int64
	var investmentCount int

	// Calculate monthly investments from transactions
	for _, transaction := range currentTransactions {
		if transaction.Category == financialsheetModel.CategoryIdentifierPersonalReserves ||
			transaction.Category == financialsheetModel.CategoryIdentifierNetworthInvestments {
			monthlyInvestments += int64(transaction.Value)
			investmentCount++
		}
	}

	// Calculate net worth from financial map
	var netWorth int64
	if financialMap.StrategicFund != nil {
		netWorth += int64(financialMap.StrategicFund.CurrentValue)
	}
	netWorth += int64(financialMap.TotalInvestments)
	netWorth += int64(financialMap.TotalAssets)

	// Calculate monthly expenses from financial stress
	var monthlyExpenses int64
	if financialStress != nil && financialStress.ExpenseAnalysis != nil {
		monthlyExpenses = int64(financialStress.ExpenseAnalysis.FixedExpenses.Total) +
			int64(financialStress.ExpenseAnalysis.VariableExpenses.Total) +
			int64(financialStress.ExpenseAnalysis.Debts.Total)
	}

	// Calculate savings rate
	var savingsRate float64
	if financialMap.MonthlyIncome > 0 {
		savings := int64(financialMap.MonthlyIncome) - monthlyExpenses
		savingsRate = (float64(savings) / float64(financialMap.MonthlyIncome)) * 100
	}

	return CurrentMetricsDTO{
		NetWorth:             netWorth,
		MonthlyExpenses:      monthlyExpenses,
		MonthlyInvestments:   monthlyInvestments,
		MonthlyIncome:        int64(financialMap.MonthlyIncome),
		SavingsRate:          savingsRate,
		InvestmentGrowthRate: 8.5, // Placeholder - would need actual calculation
		YearsInvesting:       3.5, // Placeholder - would need actual calculation
	}
}

func (s *service) buildFICalculations(currentMetrics CurrentMetricsDTO) FICalculationsDTO {
	// Calculate FI targets based on 25x annual expenses rule
	annualExpenses := currentMetrics.MonthlyExpenses * 12
	leanFiTarget := annualExpenses * 20     // 20x for lean FI
	standardFiTarget := annualExpenses * 25 // 25x for standard FI
	fatFiTarget := annualExpenses * 30      // 30x for fat FI

	// Calculate current FI progress
	var currentFiProgress float64
	if standardFiTarget > 0 {
		currentFiProgress = (float64(currentMetrics.NetWorth) / float64(standardFiTarget)) * 100
	}

	// Calculate months to FI (simplified calculation)
	var monthsToLeanFi, monthsToStandardFi, monthsToFatFi int
	if currentMetrics.MonthlyInvestments > 0 {
		remainingLean := leanFiTarget - currentMetrics.NetWorth
		remainingStandard := standardFiTarget - currentMetrics.NetWorth
		remainingFat := fatFiTarget - currentMetrics.NetWorth

		if remainingLean > 0 {
			monthsToLeanFi = int(remainingLean / currentMetrics.MonthlyInvestments)
		}
		if remainingStandard > 0 {
			monthsToStandardFi = int(remainingStandard / currentMetrics.MonthlyInvestments)
		}
		if remainingFat > 0 {
			monthsToFatFi = int(remainingFat / currentMetrics.MonthlyInvestments)
		}
	}

	return FICalculationsDTO{
		LeanFiTarget:       leanFiTarget,
		StandardFiTarget:   standardFiTarget,
		FatFiTarget:        fatFiTarget,
		CurrentFiProgress:  currentFiProgress,
		MonthsToLeanFi:     monthsToLeanFi,
		MonthsToStandardFi: monthsToStandardFi,
		MonthsToFatFi:      monthsToFatFi,
	}
}

func (s *service) buildMilestoneTracking(currentMetrics CurrentMetricsDTO, fiCalculations FICalculationsDTO) []MilestoneTrackingDTO {
	var milestones []MilestoneTrackingDTO

	// Emergency fund milestone (6 months of expenses)
	emergencyFundTarget := currentMetrics.MonthlyExpenses * 6
	emergencyFundCurrent := currentMetrics.NetWorth // Simplified - would need actual emergency fund amount
	if emergencyFundCurrent > emergencyFundTarget {
		emergencyFundCurrent = emergencyFundTarget
	}

	emergencyFundProgress := float64(0)
	if emergencyFundTarget > 0 {
		emergencyFundProgress = (float64(emergencyFundCurrent) / float64(emergencyFundTarget)) * 100
	}

	milestones = append(milestones, MilestoneTrackingDTO{
		Milestone:       "emergency_fund_complete",
		TargetAmount:    emergencyFundTarget,
		CurrentAmount:   emergencyFundCurrent,
		ProgressPercent: emergencyFundProgress,
		MonthsRemaining: 6, // Placeholder
	})

	// First 100k milestone
	first100k := int64(100000)
	first100kProgress := float64(0)
	if first100k > 0 {
		first100kProgress = (float64(currentMetrics.NetWorth) / float64(first100k)) * 100
		if first100kProgress > 100 {
			first100kProgress = 100
		}
	}

	milestone100k := MilestoneTrackingDTO{
		Milestone:       "first_100k",
		TargetAmount:    first100k,
		CurrentAmount:   currentMetrics.NetWorth,
		ProgressPercent: first100kProgress,
	}

	if currentMetrics.NetWorth >= first100k {
		milestone100k.CompletedDate = "2024-08-15" // Placeholder
	} else {
		remainingAmount := first100k - currentMetrics.NetWorth
		if currentMetrics.MonthlyInvestments > 0 {
			milestone100k.MonthsRemaining = int(remainingAmount / currentMetrics.MonthlyInvestments)
		}
	}

	milestones = append(milestones, milestone100k)

	return milestones
}

func (s *service) buildHistoricalProgress(netWorthHistory []*dashboardModel.NetWorthSnapshot) []HistoricalProgressDTO {
	var progress []HistoricalProgressDTO

	for _, snapshot := range netWorthHistory {
		progress = append(progress, HistoricalProgressDTO{
			Date:               snapshot.Date.Format("2006-01-02"),
			NetWorth:           int64(snapshot.TotalValue),
			MonthlyInvestments: 1000, // Placeholder - would need actual calculation
			FiProgress:         11.4, // Placeholder - would need actual calculation
		})
	}

	return progress
}

func (s *service) buildRetirementProjections(userProfile *model.User, currentMetrics CurrentMetricsDTO, fiCalculations FICalculationsDTO) RetirementProjectionsDTO {
	// Calculate age from birth date if available
	currentAge := 28          // Placeholder - would calculate from userProfile.BirthDate
	targetRetirementAge := 45 // Placeholder - could be configurable
	yearsToRetirement := targetRetirementAge - currentAge

	// Project net worth at retirement (simplified calculation)
	projectedNetWorth := currentMetrics.NetWorth + (currentMetrics.MonthlyInvestments * 12 * int64(yearsToRetirement))

	return RetirementProjectionsDTO{
		CurrentAge:                    currentAge,
		TargetRetirementAge:           targetRetirementAge,
		YearsToRetirement:             yearsToRetirement,
		ProjectedNetWorthAtRetirement: projectedNetWorth,
		RequiredMonthlyInvestment:     currentMetrics.MonthlyInvestments,
	}
}

// Helper methods for Gamification Engagement Data API

func (s *service) buildEngagementMetrics(record *financialsheetModel.Record) EngagementMetricsDTO {
	var currentStreak, longestStreak, totalActiveDays int
	var lastActivityDate string

	if record != nil {
		currentStreak = record.Points.Current
		longestStreak = record.Points.Best
		totalActiveDays = 120 // Placeholder - would need actual calculation

		if !record.Points.LastTransactionDate.IsZero() {
			lastActivityDate = record.Points.LastTransactionDate.Format("2006-01-02T15:04:05Z")
		}
	}

	return EngagementMetricsDTO{
		CurrentStreak:          currentStreak,
		LongestStreak:          longestStreak,
		TotalActiveDays:        totalActiveDays,
		WeeklyActiveHours:      8.5, // Placeholder
		LastActivityDate:       lastActivityDate,
		AverageSessionDuration: 25.5, // Placeholder
		MonthlyActiveHours:     34.0, // Placeholder
	}
}

func (s *service) buildAchievementData(userAchievements []*gamificationModel.Achievement, contentAchievements []*content.Achievement) AchievementDataDTO {
	totalEarned := len(userAchievements)
	totalAvailable := len(contentAchievements)

	var completionRate float64
	if totalAvailable > 0 {
		completionRate = (float64(totalEarned) / float64(totalAvailable)) * 100
	}

	// Build recent achievements
	var recentAchievements []RecentAchievementDTO
	for i, achievement := range userAchievements {
		if i >= 3 { // Only show last 3 achievements
			break
		}

		// Find the content achievement for details
		var contentAchievement *content.Achievement
		for _, ca := range contentAchievements {
			if ca.Identifier == achievement.Identifier {
				contentAchievement = ca
				break
			}
		}

		recentAchievement := RecentAchievementDTO{
			ID:         achievement.Identifier,
			EarnedDate: achievement.EarnedAt.Format("2006-01-02T15:04:05Z"),
		}

		if contentAchievement != nil {
			recentAchievement.Name = contentAchievement.Name
			recentAchievement.Description = contentAchievement.Description
			recentAchievement.Icon = contentAchievement.Logo
		}

		recentAchievements = append(recentAchievements, recentAchievement)
	}

	// Build category progress
	categoryProgress := map[string]int{
		"financial_planning": 3,
		"investment":         2,
		"budgeting":          4,
		"education":          3,
	}

	return AchievementDataDTO{
		TotalEarned:        totalEarned,
		TotalAvailable:     totalAvailable,
		CompletionRate:     completionRate,
		RecentAchievements: recentAchievements,
		CategoryProgress:   categoryProgress,
	}
}

func (s *service) buildLeagueData(ctx context.Context, userID string, record *financialsheetModel.Record) LeagueDataDTO {
	// Placeholder data - would need actual league service integration
	return LeagueDataDTO{
		CurrentLeague:     "Silver",
		Position:          3,
		TotalMembers:      15,
		Points:            1250,
		MonthlyPoints:     350,
		TransactionStreak: record.Points.Current,
		LeagueHistory: []LeagueHistoryDTO{
			{
				Month:    "2024-12",
				League:   "Bronze",
				Position: 2,
				Points:   900,
			},
		},
	}
}

func (s *service) buildVaultData(vault *model.Vault) VaultDataDTO {
	var currentCoins, totalEarned, totalSpent, monthlyEarned int
	var lastEarningDate string

	if vault != nil {
		currentCoins = int(vault.Coins)
		totalEarned = 2400             // Placeholder - would need actual calculation
		totalSpent = 1550              // Placeholder - would need actual calculation
		monthlyEarned = 200            // Placeholder - would need actual calculation
		lastEarningDate = "2025-01-06" // Placeholder
	}

	return VaultDataDTO{
		CurrentCoins:    currentCoins,
		TotalEarned:     totalEarned,
		TotalSpent:      totalSpent,
		MonthlyEarned:   monthlyEarned,
		LastEarningDate: lastEarningDate,
		EarningHistory: []EarningHistoryDTO{
			{
				Date:   "2025-01-05",
				Amount: 50,
				Source: "lesson_completion",
			},
		},
	}
}

// Helper methods for Family Financial DNA Data API

func (s *service) buildUserDNA(dnaTree *financialdnaModel.FinancialDNATree) UserDNADTO {
	var currentProfile string
	var profileScore float64
	var lastAssessment string

	if dnaTree != nil {
		// Extract user's DNA profile from the tree
		if len(dnaTree.Members) > 0 {
			userMember := dnaTree.Members[0] // Assume first member is the user
			currentProfile = string(userMember.FinancialStatus)
			profileScore = 75.5 // Placeholder calculation
		}

		if !dnaTree.UpdatedAt.IsZero() {
			lastAssessment = dnaTree.UpdatedAt.Format("2006-01-02T15:04:05Z")
		}
	}

	return UserDNADTO{
		CurrentProfile: currentProfile,
		ProfileScore:   profileScore,
		Characteristics: []CharacteristicDTO{
			{
				Name:        "Risk Tolerance",
				Score:       7.5,
				Description: "Moderate risk tolerance with conservative tendencies",
				Category:    "investment",
			},
			{
				Name:        "Spending Control",
				Score:       8.2,
				Description: "Good control over spending habits",
				Category:    "budgeting",
			},
		},
		StrengthAreas:    []string{"budgeting", "goal_setting"},
		ImprovementAreas: []string{"investment_knowledge", "emergency_fund"},
		LastAssessment:   lastAssessment,
	}
}

func (s *service) buildFamilyComparison(ctx context.Context, userID string, user *model.User) FamilyComparisonDTO {
	// Placeholder family comparison data
	return FamilyComparisonDTO{
		FamilyMembers: []FamilyMemberDNADTO{
			{
				UserID:       "parent1",
				Name:         "Parent 1",
				Relationship: "parent",
				Profile:      "balanced",
				Similarity:   78.5,
			},
			{
				UserID:       "parent2",
				Name:         "Parent 2",
				Relationship: "parent",
				Profile:      "investor",
				Similarity:   65.2,
			},
		},
		CommonTraits:        []string{"goal_oriented", "conservative", "family_focused"},
		DiversityIndex:      0.65,
		CollectiveStrength:  "Strong financial planning foundation",
		FamilyGoalAlignment: 82.3,
	}
}

func (s *service) buildDNAEvolution(dnaTree *financialdnaModel.FinancialDNATree) DNAEvolutionDTO {
	return DNAEvolutionDTO{
		HistoricalProfiles: []HistoricalProfileDTO{
			{
				Date:    "2024-06-01",
				Profile: "indebted",
				Score:   45.2,
				Trigger: "initial_assessment",
			},
			{
				Date:    "2024-09-01",
				Profile: "balanced",
				Score:   68.7,
				Trigger: "financial_education",
			},
		},
		EvolutionTrend:    "improving",
		StabilityScore:    72.5,
		NextAssessmentDue: "2025-03-01",
	}
}

func (s *service) buildBehaviorInsights(dnaTree *financialdnaModel.FinancialDNATree) []BehaviorInsightDTO {
	return []BehaviorInsightDTO{
		{
			Category:   "spending",
			Insight:    "Shows consistent improvement in spending control over the last 6 months",
			Impact:     "positive",
			Confidence: 85.5,
			ActionItems: []string{
				"Continue current budgeting practices",
				"Consider increasing emergency fund target",
			},
		},
		{
			Category:   "investment",
			Insight:    "Limited investment knowledge may be hindering portfolio growth",
			Impact:     "neutral",
			Confidence: 72.3,
			ActionItems: []string{
				"Complete investment education modules",
				"Start with low-risk investment options",
			},
		},
	}
}

func (s *service) buildDNARecommendations(dnaTree *financialdnaModel.FinancialDNATree) []DNARecommendationDTO {
	return []DNARecommendationDTO{
		{
			Type:        "education",
			Title:       "Investment Fundamentals Course",
			Description: "Based on your DNA profile, focusing on investment education would accelerate your financial growth",
			Priority:    "high",
			Actions: []string{
				"Complete 'Investment Basics' trail",
				"Practice with investment simulator",
				"Set up automatic investment plan",
			},
			ExpectedOutcome: "Improved investment confidence and 15-20% portfolio growth potential",
		},
		{
			Type:        "planning",
			Title:       "Emergency Fund Optimization",
			Description: "Your current emergency fund could be optimized for better returns while maintaining liquidity",
			Priority:    "medium",
			Actions: []string{
				"Review current emergency fund allocation",
				"Consider high-yield savings options",
				"Set up automatic contributions",
			},
			ExpectedOutcome: "Better emergency fund returns without compromising accessibility",
		},
	}
}

// Helper methods for Transaction Behavior Data API

func (s *service) buildTransactionPatterns(record *financialsheetModel.Record) TransactionPatternsDTO {
	// Placeholder transaction pattern analysis
	return TransactionPatternsDTO{
		AverageTransactionValue: 125.50,
		TransactionFrequency:    "daily",
		MostActiveDay:           "Friday",
		MostActiveTimeOfDay:     "evening",
		ConsistencyScore:        78.5,
		RecurringTransactions: []RecurringTransactionDTO{
			{
				Description: "Monthly Salary",
				Amount:      5000.00,
				Frequency:   "monthly",
				Category:    "income",
				Confidence:  95.2,
			},
		},
		ImpulseTransactionRate: 15.3,
	}
}

func (s *service) buildSpendingBehavior(record *financialsheetModel.Record, financialMap *dashboardModel.FinancialMap) SpendingBehaviorDTO {
	return SpendingBehaviorDTO{
		MonthlySpendingTrend: "increasing",
		SeasonalPatterns: []SeasonalPatternDTO{
			{
				Period:           "December",
				SpendingIncrease: 25.5,
				MainCategories:   []string{"entertainment", "gifts", "food"},
			},
		},
		BudgetAdherence:         82.3,
		EmergencySpendingRate:   8.7,
		PlannedVsUnplannedRatio: 75.2,
		PaymentMethodPreferences: []PaymentMethodUsageDTO{
			{
				Method:        "credit_card",
				Percentage:    65.5,
				AverageAmount: 180.25,
			},
			{
				Method:        "debit_card",
				Percentage:    25.8,
				AverageAmount: 85.40,
			},
		},
	}
}

func (s *service) buildCategoryAnalysis(record *financialsheetModel.Record) CategoryAnalysisDTO {
	return CategoryAnalysisDTO{
		TopCategories: []CategorySpendingDTO{
			{
				Category:         "food",
				Amount:           1250.00,
				Percentage:       35.2,
				TransactionCount: 45,
			},
			{
				Category:         "transportation",
				Amount:           850.00,
				Percentage:       24.1,
				TransactionCount: 28,
			},
		},
		CategoryTrends: []CategoryTrendDTO{
			{
				Category: "food",
				Trend:    "increasing",
				Change:   12.5,
			},
		},
		CategoryConsistency: map[string]float64{
			"food":           85.2,
			"transportation": 78.9,
			"entertainment":  65.4,
		},
		UnusualSpending: []UnusualSpendingDTO{
			{
				Date:           "2025-01-05",
				Amount:         450.00,
				Category:       "entertainment",
				Description:    "Concert tickets",
				DeviationScore: 2.8,
			},
		},
	}
}

func (s *service) buildTemporalAnalysis(record *financialsheetModel.Record) TemporalAnalysisDTO {
	return TemporalAnalysisDTO{
		WeeklyPatterns: []WeeklyPatternDTO{
			{
				DayOfWeek: "Monday",
				Amount:    125.50,
				Count:     8,
			},
			{
				DayOfWeek: "Friday",
				Amount:    285.75,
				Count:     15,
			},
		},
		MonthlyPatterns: []MonthlyPatternDTO{
			{
				Month:  "January",
				Amount: 3250.00,
				Count:  125,
			},
		},
		HourlyPatterns: []HourlyPatternDTO{
			{
				Hour:   12,
				Amount: 45.25,
				Count:  25,
			},
			{
				Hour:   18,
				Amount: 85.50,
				Count:  35,
			},
		},
		StreakAnalysis: StreakAnalysisDTO{
			CurrentStreak:       record.Points.Current,
			LongestStreak:       record.Points.Best,
			StreakConsistency:   85.5,
			LastTransactionDate: record.Points.LastTransactionDate.Format("2006-01-02T15:04:05Z"),
		},
	}
}

func (s *service) buildTransactionInsights(record *financialsheetModel.Record) []TransactionInsightDTO {
	return []TransactionInsightDTO{
		{
			Type:       "spending_pattern",
			Insight:    "Your spending increases significantly on weekends, particularly on entertainment",
			Impact:     "moderate",
			Confidence: 82.5,
			Evidence:   []string{"Weekend spending 40% higher than weekdays", "Entertainment category peaks on Saturdays"},
			Suggestions: []string{
				"Set weekend spending limits",
				"Plan entertainment budget in advance",
			},
		},
		{
			Type:       "consistency",
			Insight:    "You maintain excellent transaction recording consistency",
			Impact:     "positive",
			Confidence: 95.2,
			Evidence:   []string{"15-day current streak", "25-day longest streak"},
			Suggestions: []string{
				"Continue current tracking habits",
				"Consider setting up automatic transaction imports",
			},
		},
	}
}

func (s *service) buildBehaviorRecommendations(record *financialsheetModel.Record) []BehaviorRecommendationDTO {
	return []BehaviorRecommendationDTO{
		{
			Type:        "budgeting",
			Title:       "Weekend Spending Control",
			Description: "Your weekend spending patterns show room for optimization without sacrificing enjoyment",
			Priority:    "medium",
			Actions: []string{
				"Set a weekly entertainment budget",
				"Use the 50/30/20 rule for weekend expenses",
				"Track weekend vs weekday spending separately",
			},
			ExpectedImpact: "Potential 15-20% reduction in entertainment expenses while maintaining lifestyle quality",
		},
		{
			Type:        "automation",
			Title:       "Transaction Tracking Automation",
			Description: "Your excellent manual tracking could be enhanced with automation for even better insights",
			Priority:    "low",
			Actions: []string{
				"Connect bank accounts for automatic imports",
				"Set up spending alerts for unusual transactions",
				"Use category auto-assignment rules",
			},
			ExpectedImpact: "Reduced manual effort while maintaining tracking accuracy and gaining real-time insights",
		},
	}
}
