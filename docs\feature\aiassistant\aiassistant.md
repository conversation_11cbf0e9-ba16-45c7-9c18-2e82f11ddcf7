# AI Assistant APIs for Financial Education Platform

**Date**: 2025-01-06  
**Priority**: High  
**Target**: AI Financial Advisor Integration  

---

## 1. Overview

The AI Assistant APIs provide a comprehensive set of data aggregation endpoints designed specifically for external AI financial advisor agents. These APIs serve as pure data providers, delivering rich, factual user information without analysis or recommendations, enabling external AI systems to perform intelligent financial guidance and decision-making.

### 1.1. Purpose

- **Pure Data Aggregation**: Provide comprehensive, factual user financial data without interpretation
- **External AI Enablement**: Supply structured data for external AI systems to analyze and generate recommendations
- **Comprehensive Context**: Deliver complete financial picture across all platform components
- **Factual Foundation**: Ensure AI recommendations are based on accurate, up-to-date user data

### 1.2. Design Philosophy

The 10 AI assistant APIs are designed as **data providers**, not recommendation engines:

- **Factual Only**: Return measurable, objective data without subjective analysis or scoring
- **Comprehensive Coverage**: Each API provides deep data in specific financial domains
- **AI-Optimized Structure**: JSON responses designed for easy parsing and pattern recognition
- **Mathematical Calculations**: Include computed metrics (percentages, totals, averages) without interpretation
- **Historical Context**: Provide trend data and historical patterns for AI analysis
- **External Decision Making**: Enable external AI to perform all analysis, inference, and recommendation generation

---

## 2. API Specifications

### 2.1. Financial Health Overview API

**Endpoint**: `GET /v2/aiassistant/financial-health-overview`

**Purpose**: Provides comprehensive financial health data including income, expenses, stress metrics, and net worth trends. Serves as primary data source for external AI to assess overall financial situation.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "monthlyFinancials": {
    "income": 5000,
    "expenses": 3500,
    "savings": 1500,
    "savingsRate": 30.0,
    "fixedExpenses": 2000,
    "variableExpenses": 1200,
    "debtPayments": 300
  },
  "stressMetrics": {
    "commitmentRatio": 0.7,
    "emergencyFundMonths": 3.5,
    "debtToIncomeRatio": 0.15,
    "expenseGrowthRate": 5.2
  },
  "netWorthData": {
    "current": 125000,
    "previousMonth": 120000,
    "changePercent": 4.17,
    "strategicFund": 18000,
    "investments": 25000,
    "assets": 80000,
    "debts": 15000
  },
  "historicalTrends": [
    {
      "month": "2024-12",
      "netWorth": 120000,
      "income": 4800,
      "expenses": 3400
    }
  ],
  "dataTimestamp": "2025-01-06T10:00:00Z"
}
```

**Use Case**: When external AI needs comprehensive financial data to assess user's financial health and provide recommendations.

### 2.2. Dream Progress Analysis API

**Endpoint**: `GET /v2/aiassistant/dream-progress-analysis`

**Purpose**: Provides detailed financial goals data including progress metrics, funding patterns, and timeline information. Enables external AI to analyze goal achievement patterns and provide guidance.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "dreamsSummary": {
    "totalDreams": 5,
    "activeDreams": 3,
    "completedDreams": 2,
    "totalTargetAmount": 150000,
    "totalSavedAmount": 45000,
    "overallProgress": 30.0,
    "averageMonthlyContribution": 1200
  },
  "activeDreams": [
    {
      "id": "dream_123",
      "name": "Casa Própria",
      "category": "Familiar",
      "targetAmount": 100000,
      "savedAmount": 25000,
      "progressPercent": 25.0,
      "monthlyContribution": 2000,
      "monthsElapsed": 12,
      "targetDate": "2027-03-15",
      "createdDate": "2024-01-15",
      "fundingSource": "salary_savings",
      "isShared": true,
      "contributorsCount": 2,
      "timeframe": "long_term"
    }
  ],
  "progressMetrics": {
    "dreamsOnSchedule": 2,
    "dreamsBehindSchedule": 1,
    "averageProgressRate": 8.5,
    "totalMonthlyCommitment": 3500,
    "completionRate": 40.0
  },
  "contributionHistory": [
    {
      "month": "2024-12",
      "totalContributions": 2000,
      "dreamId": "dream_123",
      "amount": 2000
    }
  ]
}
```

**Use Case**: When external AI needs comprehensive goal data to analyze progress patterns and provide goal-oriented financial guidance.

### 2.3. Learning Path Data API

**Endpoint**: `GET /v2/aiassistant/learning-path-data`

**Purpose**: Provides comprehensive educational progress data and learning patterns. Enables external AI to analyze knowledge gaps and suggest appropriate learning paths.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "learningProgress": {
    "completedTrails": 3,
    "totalTrails": 8,
    "completedLessons": 45,
    "totalLessons": 120,
    "completedChallenges": 12,
    "totalChallenges": 24,
    "overallProgress": 37.5,
    "averageCompletionTime": 14.5
  },
  "currentTrails": [
    {
      "trailId": "trail_investment",
      "name": "Investimentos",
      "progress": 60.0,
      "lessonsCompleted": 6,
      "totalLessons": 10,
      "startDate": "2024-12-01",
      "lastActivity": "2025-01-05",
      "category": "investment"
    }
  ],
  "completedTrails": [
    {
      "trailId": "trail_budgeting",
      "name": "Orçamento",
      "completedDate": "2024-11-15",
      "totalTime": 12,
      "category": "budgeting"
    }
  ],
  "learningPatterns": {
    "averageSessionDuration": 25.5,
    "preferredLearningDays": ["Monday", "Wednesday", "Friday"],
    "completionRate": 85.0,
    "streakDays": 15,
    "longestStreak": 45
  },
  "achievements": {
    "earned": 8,
    "total": 25,
    "recentAchievements": [
      {
        "id": "first_investment",
        "name": "Primeiro Investimento",
        "earnedDate": "2024-12-20",
        "category": "investment"
      }
    ]
  }
}
```

**Use Case**: When external AI needs learning data to identify knowledge gaps and suggest appropriate educational content.

### 2.4. Spending Analysis Data API

**Endpoint**: `GET /v2/aiassistant/spending-analysis-data`

**Purpose**: Provides comprehensive spending data with detailed categorization, patterns, and trends. Enables external AI to analyze spending behavior and identify optimization opportunities.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "spendingOverview": {
    "totalMonthlyExpenses": 3500,
    "fixedExpenses": 2000,
    "variableExpenses": 1200,
    "debtPayments": 300,
    "expenseGrowthRate": 5.2,
    "averageTransactionSize": 87.50
  },
  "categoryBreakdown": [
    {
      "category": "Alimentação",
      "amount": 800,
      "percentage": 22.9,
      "transactionCount": 18,
      "averageTransactionSize": 44.44,
      "monthlyChange": 12.5,
      "lastMonthAmount": 712,
      "yearToDateAmount": 9600
    }
  ],
  "spendingPatterns": {
    "peakSpendingDays": ["Friday", "Saturday"],
    "averageTransactionsPerDay": 3.2,
    "weekendVsWeekdayRatio": 1.4,
    "peakSpendingHours": ["12:00-14:00", "18:00-20:00"],
    "seasonalVariations": [
      {
        "month": "December",
        "amount": 4200,
        "changePercent": 20.0
      }
    ]
  },
  "paymentMethodBreakdown": [
    {
      "method": "credit_card",
      "percentage": 65.0,
      "amount": 2275,
      "transactionCount": 19,
      "averageAmount": 119.74
    }
  ],
  "historicalTrends": [
    {
      "month": "2024-12",
      "totalExpenses": 3400,
      "fixedExpenses": 2000,
      "variableExpenses": 1100,
      "debtPayments": 300
    }
  ]
}
```

**Use Case**: When external AI needs detailed spending data to analyze patterns and provide budgeting guidance.

### 2.5. Investment Profile Data API

**Endpoint**: `GET /v2/aiassistant/investment-profile-data`

**Purpose**: Provides comprehensive investment-related data including current portfolio, capacity metrics, and knowledge level. Enables external AI to assess investment readiness and provide guidance.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "currentPortfolio": {
    "totalValue": 25000,
    "monthlyContributions": 800,
    "investmentTypes": [
      {
        "type": "stocks",
        "value": 15000,
        "percentage": 60.0
      },
      {
        "type": "bonds",
        "value": 7000,
        "percentage": 28.0
      }
    ],
    "portfolioAge": 18
  },
  "financialCapacity": {
    "monthlyIncome": 5000,
    "monthlyExpenses": 3500,
    "availableForInvestment": 1200,
    "emergencyFundMonths": 3.5,
    "debtToIncomeRatio": 0.15,
    "savingsRate": 30.0
  },
  "investmentKnowledge": {
    "completedInvestmentTrails": 2,
    "totalInvestmentTrails": 5,
    "investmentEducationProgress": 40.0,
    "challengesCompleted": 3,
    "lastLearningActivity": "2025-01-05"
  },
  "investmentBehavior": {
    "averageMonthlyInvestment": 800,
    "investmentConsistency": 85.0,
    "monthsInvesting": 18,
    "largestSingleInvestment": 2000,
    "investmentFrequency": "monthly"
  },
  "riskFactors": {
    "ageRange": "25-35",
    "timeHorizon": "long_term",
    "dependents": 0,
    "jobStability": "stable",
    "incomeVariability": "low"
  }
}
```

**Use Case**: When external AI needs investment-related data to assess readiness and provide investment guidance.

### 2.6. Financial Independence Data API

**Endpoint**: `GET /v2/aiassistant/financial-independence-data`

**Purpose**: Provides comprehensive financial independence metrics, calculations, and milestone data. Enables external AI to analyze FI progress and provide long-term planning guidance.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "currentMetrics": {
    "netWorth": 125000,
    "monthlyExpenses": 3500,
    "monthlyInvestments": 1000,
    "monthlyIncome": 5000,
    "savingsRate": 28.6,
    "investmentGrowthRate": 8.5,
    "yearsInvesting": 3.5
  },
  "fiCalculations": {
    "leanFiTarget": 750000,
    "standardFiTarget": 1050000,
    "fatFiTarget": 1400000,
    "currentFiProgress": 11.9,
    "monthsToLeanFi": 147,
    "monthsToStandardFi": 183,
    "monthsToFatFi": 225
  },
  "milestoneTracking": [
    {
      "milestone": "emergency_fund_complete",
      "targetAmount": 21000,
      "currentAmount": 18000,
      "progressPercent": 85.7,
      "monthsRemaining": 2
    },
    {
      "milestone": "first_100k",
      "targetAmount": 100000,
      "currentAmount": 125000,
      "progressPercent": 100.0,
      "completedDate": "2024-08-15"
    }
  ],
  "historicalProgress": [
    {
      "date": "2024-12-01",
      "netWorth": 120000,
      "monthlyInvestments": 950,
      "fiProgress": 11.4
    }
  ],
  "retirementProjections": {
    "currentAge": 28,
    "targetRetirementAge": 45,
    "yearsToRetirement": 17,
    "projectedNetWorthAtRetirement": 1200000,
    "requiredMonthlyInvestment": 1000
  }
}
```

**Use Case**: When external AI needs FI data to analyze retirement readiness and provide long-term financial planning guidance.

### 2.7. Gamification Engagement Data API

**Endpoint**: `GET /v2/aiassistant/gamification-engagement-data`

**Purpose**: Provides comprehensive user engagement and gamification data including streaks, achievements, and participation patterns. Enables external AI to analyze motivation and engagement levels.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "engagementMetrics": {
    "currentStreak": 15,
    "longestStreak": 45,
    "totalActiveDays": 120,
    "weeklyActiveHours": 8.5,
    "lastActivityDate": "2025-01-06T09:30:00Z",
    "averageSessionDuration": 25.5,
    "monthlyActiveHours": 34.0
  },
  "achievementData": {
    "totalEarned": 12,
    "totalAvailable": 30,
    "completionRate": 40.0,
    "recentAchievements": [
      {
        "id": "budget_master",
        "name": "Mestre do Orçamento",
        "earnedDate": "2025-01-05T14:20:00Z",
        "category": "financial_planning",
        "difficulty": "medium"
      }
    ],
    "categoryProgress": {
      "financial_planning": 3,
      "investment": 2,
      "budgeting": 4,
      "education": 3
    }
  },
  "leagueData": {
    "currentLeague": "Silver",
    "position": 3,
    "totalMembers": 15,
    "points": 1250,
    "monthlyPoints": 350,
    "transactionStreak": 12,
    "leagueHistory": [
      {
        "month": "2024-12",
        "league": "Bronze",
        "position": 2,
        "points": 900
      }
    ]
  },
  "vaultData": {
    "currentCoins": 850,
    "totalEarned": 2400,
    "totalSpent": 1550,
    "monthlyEarned": 200,
    "lastEarningDate": "2025-01-06",
    "earningHistory": [
      {
        "date": "2025-01-05",
        "amount": 50,
        "source": "lesson_completion"
      }
    ]
  }
}
```

**Use Case**: When external AI needs engagement data to assess user motivation and provide appropriate encouragement strategies.

### 2.8. Family Financial DNA Data API

**Endpoint**: `GET /v2/aiassistant/family-financial-dna-data`

**Purpose**: Provides comprehensive family financial pattern data and generational information. Enables external AI to analyze family financial dynamics and identify behavioral patterns.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "treeCompleteness": {
    "totalMembers": 12,
    "completedProfiles": 8,
    "completionPercentage": 66.7,
    "generationsCovered": 3,
    "lastUpdated": "2025-01-05"
  },
  "familyProfiles": {
    "profileDistribution": {
      "investor": 25.0,
      "balanced": 50.0,
      "indebted": 16.7,
      "overindebted": 8.3,
      "undefined": 0.0
    },
    "generationalBreakdown": [
      {
        "generation": "grandparents",
        "memberCount": 4,
        "completedProfiles": 2,
        "dominantProfile": "balanced",
        "averageAge": 75
      },
      {
        "generation": "parents",
        "memberCount": 2,
        "completedProfiles": 2,
        "dominantProfile": "investor",
        "averageAge": 52
      }
    ]
  },
  "behavioralPatterns": {
    "identifiedPatterns": [
      {
        "pattern": "high_savings_rate",
        "frequency": 75.0,
        "generations": ["grandparents", "parents"],
        "memberCount": 6
      },
      {
        "pattern": "conservative_investing",
        "frequency": 50.0,
        "generations": ["parents"],
        "memberCount": 2
      }
    ],
    "breakCycles": [
      {
        "category": "impulse_spending",
        "affectedMembers": 3,
        "generationsAffected": ["self", "siblings"]
      }
    ]
  },
  "familyCollaboration": {
    "sharedDreams": 2,
    "totalFamilyMembers": 12,
    "activeCollaborators": 4,
    "collectiveProgress": 35.0,
    "monthlyFamilyContributions": 1500
  },
  "treeProgress": {
    "overallProgress": 66.7,
    "lastMemberAdded": "2024-12-15",
    "missingGenerations": ["great_grandparents"],
    "incompleteProfiles": 4
  }
}
```

**Use Case**: When external AI needs family financial data to analyze generational patterns and provide family-oriented financial guidance.

### 2.9. Transaction Behavior Data API

**Endpoint**: `GET /v2/aiassistant/transaction-behavior-data`

**Purpose**: Provides comprehensive transaction pattern data including timing, frequency, and behavioral indicators. Enables external AI to analyze spending habits and identify behavioral patterns.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "transactionPatterns": {
    "averageTransactionsPerDay": 4.2,
    "totalTransactionsLastMonth": 126,
    "averageTransactionAmount": 87.50,
    "medianTransactionAmount": 45.00,
    "largestTransaction": 850.00,
    "smallestTransaction": 5.50
  },
  "timingPatterns": {
    "peakSpendingHours": ["12:00-14:00", "18:00-20:00"],
    "peakSpendingDays": ["Friday", "Saturday"],
    "weekendVsWeekdayRatio": 1.3,
    "monthlySpendingDistribution": [
      {
        "week": 1,
        "percentage": 35.0,
        "amount": 1225
      }
    ]
  },
  "categoryFrequency": [
    {
      "category": "Alimentação",
      "transactionCount": 18,
      "frequency": "daily",
      "averageAmount": 44.44,
      "timePattern": "lunch_dinner"
    }
  ],
  "paymentMethodData": {
    "methodDistribution": [
      {
        "method": "credit_card",
        "percentage": 65.0,
        "transactionCount": 82,
        "averageAmount": 119.74
      },
      {
        "method": "pix",
        "percentage": 25.0,
        "transactionCount": 32,
        "averageAmount": 67.50
      }
    ],
    "creditCardUsage": {
      "utilizationRate": 35.0,
      "averageMonthlySpending": 2275,
      "paymentHistory": "on_time"
    }
  },
  "seasonalData": [
    {
      "month": "December",
      "totalSpending": 4200,
      "increasePercent": 45.0,
      "topCategories": ["gifts", "travel", "food"]
    }
  ],
  "recurringTransactions": [
    {
      "description": "Netflix Subscription",
      "amount": 29.90,
      "frequency": "monthly",
      "category": "entertainment",
      "lastTransaction": "2025-01-01"
    }
  ]
}
```

**Use Case**: When external AI needs transaction behavior data to analyze spending patterns and provide behavioral insights.

### 2.10. Comprehensive Financial Context API

**Endpoint**: `GET /v2/aiassistant/comprehensive-financial-context`

**Purpose**: Provides aggregated financial context data combining key metrics from all financial areas. Enables external AI to generate comprehensive financial plans and prioritized recommendations.

**Authentication**: JWT Token (AuthGuard) and (N8nGuard)

**Response Structure**:
```json
{
  "userId": "string",
  "financialSnapshot": {
    "netWorth": 125000,
    "monthlyIncome": 5000,
    "monthlyExpenses": 3500,
    "monthlySavings": 1500,
    "savingsRate": 30.0,
    "debtToIncomeRatio": 0.15,
    "emergencyFundMonths": 3.5
  },
  "goalProgress": {
    "activeDreams": 3,
    "totalDreamValue": 150000,
    "dreamProgress": 30.0,
    "monthlyDreamContributions": 1200,
    "fiProgress": 11.9,
    "fiTarget": 1050000
  },
  "learningStatus": {
    "completedTrails": 3,
    "totalTrails": 8,
    "educationProgress": 37.5,
    "lastLearningActivity": "2025-01-05",
    "knowledgeAreas": ["budgeting", "basic_investing"]
  },
  "behavioralMetrics": {
    "spendingConsistency": 85.0,
    "investmentConsistency": 90.0,
    "engagementLevel": "high",
    "currentStreak": 15,
    "transactionFrequency": 4.2
  },
  "riskFactors": {
    "incomeStability": "stable",
    "expenseVolatility": "low",
    "debtLevel": "manageable",
    "emergencyFundStatus": "adequate",
    "investmentRisk": "moderate"
  },
  "opportunities": {
    "availableMonthlyInvestment": 1200,
    "underutilizedCategories": ["investment_education"],
    "improvementAreas": ["emergency_fund", "expense_optimization"],
    "strengths": ["consistent_saving", "goal_oriented"]
  },
  "timeHorizons": {
    "shortTerm": {
      "months": 6,
      "primaryFocus": "emergency_fund_completion",
      "availableCapacity": 300
    },
    "mediumTerm": {
      "months": 24,
      "primaryFocus": "dream_achievement",
      "requiredMonthlyAction": 1200
    },
    "longTerm": {
      "years": 15,
      "primaryFocus": "financial_independence",
      "currentTrajectory": "on_track"
    }
  }
}
```

**Use Case**: When external AI needs comprehensive financial context to generate personalized action plans and strategic recommendations.

---

## 3. Implementation Notes

### 3.1. Relationship to Existing Context API

The existing `/v2/aiassistant/users/:userId/context` will be deprecated. The new `/v2/aiassistant` endpoints provide a foundational data aggregation. The new 10 APIs extend this by:

- **Specialized Data Domains**: Each API provides comprehensive data in specific financial areas
- **Enhanced Data Depth**: Detailed metrics, historical trends, and behavioral patterns
- **Pure Data Focus**: Factual information without analysis, enabling external AI decision-making
- **Comprehensive Coverage**: Complete data sets for thorough external AI analysis

### 3.2. Data Aggregation Strategy

Each API leverages multiple existing services:

- **Dashboard Service**: Financial maps, stress analysis, financial independence calculations
- **Dreamboard Service**: Goal tracking, shared dreams, progress analysis
- **Financial Sheet Service**: Transaction data, categorization, league performance
- **Financial DNA Service**: Family patterns, generational insights, cycle analysis
- **Progression Service**: Educational progress, trail completion, learning analytics
- **Gamification Service**: Achievements, engagement metrics, motivation analysis
- **Vault Service**: Coin management, reward utilization patterns
- **User Service**: Profile data, onboarding information, preferences

### 3.3. Authentication Requirements

All APIs require one of:
- **JWT Authentication**: Standard user authentication via `AuthGuard()` middleware
- **Service Authentication**: Inter-service communication via `N8nGuard()`

### 3.4. Performance Considerations

- **Concurrent Data Fetching**: Use goroutines for parallel service calls
- **Caching Strategy**: Implement caching (from cache package) for frequently accessed calculations
- **Data Projection**: Fetch only necessary fields from each service
- **Response Optimization**: Structure responses for minimal AI processing overhead

---

## 4. Integration Guidelines

### 4.1. AI Agent API Selection Strategy

The AI agent should select APIs based on user query analysis:

**Query Type → Recommended Data APIs**

- **General Financial Health**: `financial-health-overview` + `comprehensive-financial-context`
- **Goal-Related Questions**: `dream-progress-analysis` + `financial-independence-data`
- **Spending/Budget Questions**: `spending-analysis-data` + `transaction-behavior-data`
- **Investment Questions**: `investment-profile-data` + `financial-independence-data`
- **Learning/Education**: `learning-path-data` + `gamification-engagement-data`
- **Family/Behavioral**: `family-financial-dna-data` + `transaction-behavior-data`
- **Motivation/Engagement**: `gamification-engagement-data` + `learning-path-data`
- **Comprehensive Analysis**: `comprehensive-financial-context` + domain-specific APIs

### 4.2. Response Format Optimization

All APIs return JSON optimized for external AI analysis:

```json
{
  "userId": "string",
  "dataTimestamp": "ISO8601",
  "factualData": {
    "metrics": "object",
    "amounts": "object",
    "percentages": "object",
    "counts": "object"
  },
  "historicalData": ["array_of_time_series"],
  "patterns": {
    "behavioral_data": "object",
    "frequency_data": "object",
    "timing_data": "object"
  },
  "calculations": {
    "mathematical_results": "object",
    "derived_metrics": "object",
    "trend_indicators": "object"
  },
  "context_flags": {
    "data_completeness": "boolean",
    "last_updated": "ISO8601",
    "data_sources": ["array_of_sources"]
  }
}
```

### 4.3. Error Handling Strategy

**Graceful Degradation**:
- If one service fails, return partial data with `context_flags` indicating missing components
- Include `data_completeness` flags for each data source
- Provide available data without attempting to fill gaps with assumptions

**Error Response Format**:
```json
{
  "error": {
    "code": "PARTIAL_DATA_AVAILABLE",
    "message": "Some financial data unavailable",
    "available_data": ["dreams", "transactions"],
    "missing_data": ["investments", "financial_dna"],
    "data_quality": "partial"
  },
  "partial_response": "object"
}
```

### 4.4. Rate Limiting and Caching

- **Rate Limits**: 100 requests per minute per user
- **Cache TTL**: 5 minutes for real-time data, 1 hour for historical analysis
- **Cache Keys**: `aiassistant:{endpoint}:{userId}:{timestamp}`

---

## 5. Technical Implementation

### 5.1. Service Dependencies

Each API requires integration with existing services:

```go
type AIAssistantService struct {
    DashboardService      dashboard.Service
    DreamboardService     dreamboard.Service
    FinancialSheetService financialsheet.Service
    FinancialDNAService   financialdna.Service
    ProgressionService    progression.Service
    GamificationService   gamification.Service
    VaultService          vault.Service
    UserService           user.Service
    LeagueService         league.Service
    Cache                 cache.Service
}
```

### 5.2. Controller Structure

```go
// Controller interface for AI Assistant APIs
type Controller interface {
    RegisterRoutes(ctx context.Context, group *echo.Group)

    // Core Data APIs
    FinancialHealthOverview() echo.HandlerFunc
    DreamProgressAnalysis() echo.HandlerFunc
    LearningPathData() echo.HandlerFunc
    SpendingAnalysisData() echo.HandlerFunc
    InvestmentProfileData() echo.HandlerFunc
    FinancialIndependenceData() echo.HandlerFunc
    GamificationEngagementData() echo.HandlerFunc
    FamilyFinancialDNAData() echo.HandlerFunc
    TransactionBehaviorData() echo.HandlerFunc
    ComprehensiveFinancialContext() echo.HandlerFunc
}
```

### 5.3. Route Registration

```go
func (c *controller) RegisterRoutes(ctx context.Context, group *echo.Group) {
    aiGroup := group.Group("/aiassistant")

    // Authentication middleware
    authChain := []echo.MiddlewareFunc{
        middlewares.AuthGuard(),
        middlewares.N8nGuard(),
    }

    // Register all AI assistant data endpoints
    aiGroup.GET("/financial-health-overview",
        c.FinancialHealthOverview(), authChain...)
    aiGroup.GET("/dream-progress-analysis",
        c.DreamProgressAnalysis(), authChain...)
    aiGroup.GET("/learning-path-data",
        c.LearningPathData(), authChain...)
    aiGroup.GET("/spending-analysis-data",
        c.SpendingAnalysisData(), authChain...)
    aiGroup.GET("/investment-profile-data",
        c.InvestmentProfileData(), authChain...)
    aiGroup.GET("/financial-independence-data",
        c.FinancialIndependenceData(), authChain...)
    aiGroup.GET("/gamification-engagement-data",
        c.GamificationEngagementData(), authChain...)
    aiGroup.GET("/family-financial-dna-data",
        c.FamilyFinancialDNAData(), authChain...)
    aiGroup.GET("/transaction-behavior-data",
        c.TransactionBehaviorData(), authChain...)
    aiGroup.GET("/comprehensive-financial-context",
        c.ComprehensiveFinancialContext(), authChain...)
}
```

### 5.4. Database Considerations

- **Read Optimization**: Use MongoDB read preferences for analytics queries
- **Indexing Strategy**: Ensure proper indexes on userId and timestamp fields
- **Aggregation Pipelines**: Leverage MongoDB aggregation for complex calculations
- **Connection Pooling**: Optimize connection usage for concurrent service calls

---

## 6. Testing Strategy

### 6.1. Unit Tests

- **Service Layer**: Mock all dependencies, test business logic
- **Controller Layer**: Test HTTP handlers with mocked services
- **Integration Tests**: Test API responses with real data

### 6.2. Performance Tests

- **Load Testing**: Simulate concurrent AI agent requests
- **Response Time**: Target <500ms for all APIs
- **Memory Usage**: Monitor service memory consumption under load

### 6.3. Data Quality Tests

- **Completeness**: Verify all required fields are present
- **Accuracy**: Validate calculations against known test cases
- **Consistency**: Ensure data consistency across related APIs

---

## 7. Monitoring and Observability

### 7.1. Metrics

- **API Usage**: Track calls per endpoint, response times
- **Error Rates**: Monitor partial data scenarios and failures
- **Cache Performance**: Hit rates, miss rates, eviction patterns
- **Business Metrics**: Track AI recommendation effectiveness

### 7.2. Logging

- **Structured Logging**: Use consistent log format across all APIs
- **Correlation IDs**: Track requests across service boundaries
- **Performance Logging**: Log slow queries and service calls

### 7.3. Alerting

- **High Error Rates**: Alert on >5% error rate
- **Slow Response Times**: Alert on >1s average response time
- **Service Dependencies**: Alert on downstream service failures

---

## 8. Security Considerations

### 8.1. Data Privacy

- **User Consent**: Ensure user consent for AI data processing
- **Data Minimization**: Return only necessary data for AI recommendations
- **Audit Logging**: Log all AI assistant data access

### 8.2. Access Control

- **User Isolation**: Strict user ID validation and data filtering
- **Service Authentication**: Secure inter-service communication
- **Rate Limiting**: Prevent abuse and ensure fair usage

### 8.3. Data Sanitization

- **Input Validation**: Validate all user inputs and parameters
- **Output Sanitization**: Ensure no sensitive data leakage
- **Error Messages**: Avoid exposing internal system details

---

## 9. Future Enhancements

### 9.1. Enhanced Data Aggregation

- **Real-time Market Data**: Integration with financial market APIs for investment context
- **External Account Integration**: Bank and investment account data aggregation
- **Advanced Behavioral Metrics**: More sophisticated pattern recognition data

### 9.2. Real-time Data Streaming

- **WebSocket Support**: Real-time data updates for external AI systems
- **Event Streaming**: Kafka-based event processing for immediate data updates
- **Change Detection**: Automatic notifications when significant data changes occur

### 9.3. Data Quality and Enrichment

- **Data Validation**: Enhanced data quality checks and validation
- **Historical Data Expansion**: Longer historical data retention and analysis
- **Cross-Platform Integration**: Data aggregation from multiple financial platforms
